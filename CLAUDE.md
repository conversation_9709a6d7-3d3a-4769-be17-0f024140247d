# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Smart MP Biz 是一个综合性的微信和支付宝小程序业务组件库（`@wosai/smart-mp-biz`），专为智慧经营设计。项目使用 MorJS 支持多平台编译，提供模块化的电商、订餐和支付功能组件。

## 开发规范

### 代码风格规范
@include .claude/rules/coding-style.md

### 业务规范
@include .claude/rules/business-rules.md

### 安全规范
@include .claude/rules/security-guidelines.md

### 性能优化规范
@include .claude/rules/performance-guide.md

### 项目特定规范
@include .claude/rules/project-specific.md

### 组件开发指南
@include .claude/rules/component-development.md

### 主题系统规范
@include .claude/rules/theme-system.md

## 开发工作流

### 开发流程和命令
@include .claude/commands/dev-workflow.md

### 构建命令参考
@include .claude/commands/build-commands.md

## 架构概览

### 多平台架构
项目使用 MorJS 进行跨平台编译：
- **微信小程序**：主要目标平台
- **支付宝小程序**：次要目标平台，有平台特定适配
- **Web 平台**：额外的 Web 平台支持

### 模块化业务结构
业务逻辑按模块组织，通过 `package_name` 环境变量控制：
- `home` - 首页和商店浏览
- `order` - 订单管理和历史
- `store` - 商店信息和管理
- `center` - 用户中心和设置
- `purchased` - 购买历史和复购
- `love` - 收藏和集合
- `roundmeal` - 团餐功能

### 关键目录结构
- `src/pages/` - 按功能组织的页面组件
- `src/components_v2/` - 可复用业务组件（v2 架构）
- `src/lib/` - 核心库（emenu、emenuX、request、UI 组件）
- `src/stores/` - 使用自定义 store 模式的状态管理
- `src/utils/` - 工具函数和辅助函数
- `src/behaviors/` - 微信小程序 behavior，用于组件混入
- `src/styles/` - 全局样式和主题系统

### 核心依赖
- `@morjs/cli` & `@morjs/core` - 多平台编译框架
- `@wosai/emenu-mini-*` 包 - 业务逻辑和工具（v7.79.1）
- TypeScript 4.4.4 - 主要开发语言
- Jest - 测试框架

### 路径别名配置
构建系统提供了丰富的路径别名：
- `@utils` - 工具函数
- `@components_v2` - 业务组件
- `@behaviors` - 微信 behaviors
- `@services` - 业务服务
- `@styles` - 全局样式
- `@BaseComponent` - 基础组件类

## 日志约定
- 增加打印日志都要带前缀 `smart-业务相关-xxxx, @文件或者类 #方法名`

这是一个复杂的、生产就绪的小程序组件库，具有精密的构建工具和模块化架构，专为可扩展的电商应用设计。