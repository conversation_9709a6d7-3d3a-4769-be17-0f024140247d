const wx = {
  getMenuButtonBoundingClientRect: () => ({ a: 1 }),
  getSystemInfoSync: () => {
    return {
      statusBarHeight: 20,
      windowHeight: 200,
      screenWidth: 414,
      pixelRatio: 2
    }
  },
  getSystemInfo: () => {},
  showLoading: () => {},
  hideLoading: () => {},
  showModal: () => {},
  request: () => {},
  getStorageSync: () => {},
  showShareMenu: () => {}
}
module.exports = {
  testURL: 'http://0.0.0.0:10086',
  testEnvironment: 'jsdom',
  roots: [
    '<rootDir>'
    // "<rootDir>/tests"
  ],
  transform: {
    // '^.+\\.ts$': 'ts-jest',
    '^.+\\.[jt]s?(x)?$': 'ts-jest'
  },
  globals: {
    'ts-jest': {
      diagnostics: false // Disable TypeScript error checking
      // babelConfig: {
      //   presets: ['@babel/preset-env'],
      // },
    },
    getCurrentPages() {},
    getApp: function () {
      return {
        __storeReady: false
      }
    },
    my: {},
    Taro: {
      getApp: function () {
        return {
          __storeReady: false
        }
      },
      request: global.fetch
    },
    wx: wx,
    sqb: wx,
    Behavior: () => ({}),
    Component: () => ({})
  },
  testMatch: ['./**/*.(test|spec).[jt]s'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  collectCoverage: false,
  coveragePathIgnorePatterns: ['(tests/.*.mock).(jsx?|tsx?)$'],
  verbose: true,
  moduleNameMapper: {},
  // transformIgnorePatterns: ['node_modules/(?!(taro-ui|@tarojs/mobx-h5|@tarojs/taro-h5/dist/index.cjs.js)/)'],
  transformIgnorePatterns: ['node_modules/?!(valibot)']
}
