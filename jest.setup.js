global.wx = {
  constructor: { name: 'Object' },
  getSystemInfoSync: () => ({
    windowHeight: 800,
    windowWidth: 375,
    statusBarHeight: 20,
    platform: 'ios'
  }),
  getSystemInfo: jest.fn(),
  getMenuButtonBoundingClientRect: () => ({
    width: 87,
    height: 32,
    top: 24,
    right: 368,
    bottom: 56,
    left: 281
  }),
  request: jest.fn(),
  getSetting: jest.fn(),
  setClipboardData: jest.fn(),
  showToast: jest.fn(),
  makePhoneCall: jest.fn(),
  login: jest.fn(),
  navigateToMiniProgram: jest.fn(),
  getAccountInfoSync: jest.fn(() => ({
    miniProgram: {
      envVersion: 'develop'
    }
  })),
  getStorageSync: jest.fn(),
  setStorageSync: jest.fn()
}

global.my = {
  constructor: { name: 'Object' },
  getSystemInfo: jest.fn(),
  getAuthCode: jest.fn(),
  request: jest.fn()
}

global.Component = jest.fn()
global.Page = jest.fn()
global.getApp = jest.fn(() => ({
  globalData: {}
}))
global.getCurrentPages = jest.fn(() => [])
