export interface IFetchStoreDetailResp {
    data: {
        collectionNums: null;
        firstIndustry: string;
        firstIndustryCode: string;
        industry: string;
        industryCode: string;
        merchantName: string;
        merchantId: string;
        merchantSn: string;
        alipayStatus: number;
        wechatStatus: number;
        disabled: number;
        discounts: {
            globalDiscount: null;
            collectionDiscount: null;
            timeDiscount: unknown[];
            priceBreak: null;
            priceBreaks: unknown[];
            priceBreakCards: unknown[];
            returnCards: unknown[];
            membershipCards: unknown[];
            singleActivity: number;
            secondDiscounts: unknown[];
        };
        discount: null;
        organizationPath: null;
        categoriesNames: null;
        coupons: unknown[];
        storeId: string;
        storeName: string;
        storeExtraName: string;
        contactPhone: string;
        slogan: string;
        payWay: number;
        categoryId: null;
        location: {
            lat: string;
            lon: string;
        };
        storeAddress: {
            province: string;
            city: string;
            district: string;
            streetAddress: string;
            address: string;
            aoiName: null;
            houseNumber: string;
            adcode: null;
        };
        storePhoto: {
            innerPhotos: unknown[];
            outerPhotos: unknown[];
            goodsPhotos: unknown[];
            coverPhoto: string;
            logoUrl: null;
        };
        storeBasic: {
            averageConsumption: null;
            storeArea: null;
            businessHoursStart: string;
            businessHoursEnd: string;
        };
        onceOpened: boolean;
        storeType: number;
        storeSn: string;
    };
    code: number;
    message?: string | undefined;
}
