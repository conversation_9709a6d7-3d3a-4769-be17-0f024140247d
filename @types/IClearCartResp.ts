export interface IClearCartResp {
    data: {
        spu_count_map: {
            [x: string]: unknown;
        };
        total: number;
        sn: string | null;
        records: {
            id: string;
            item_id: string;
            spec_id: string;
            name: string;
            attached_info: string;
            status: number;
            price: number;
            num: number;
            ctime: number;
            mtime: number;
            category_id: string;
            url: string;
            sku: string | null;
            out_of_stock: boolean;
            user_icons: string[];
            last_deal_type: string;
            last_add_num: number;
            user_id: string;
            user_name: string;
            user_icon: string;
            last_add: boolean;
            open_table_must_order: boolean;
            open_table_must_order_editable: boolean;
            spu_type: string;
            min_sale_num: number | null;
            display_order: number;
            category_sort: number;
            materials: unknown[];
            brand_acd_product_id: (string | number) | null;
            recommend_materials: unknown[];
            attributes?: unknown | null;
            client_version?: unknown | null;
            brand_act: boolean;
        }[];
        total_price: number;
        last_deal_record: {
            id: string;
            item_id: string;
            spec_id: string;
            name: string;
            attached_info: string;
            status: number;
            price: number;
            num: number;
            ctime: number;
            mtime: number;
            category_id: string;
            url: string;
            sku: string | null;
            out_of_stock: boolean;
            user_icons: string[];
            last_deal_type: string;
            last_add_num: number;
            user_id: string;
            user_name: string;
            user_icon: string;
            last_add: boolean;
            open_table_must_order: boolean;
            open_table_must_order_editable: boolean;
            spu_type: string;
            min_sale_num: number | null;
            display_order: number;
            category_sort: number;
            materials: unknown[];
            brand_acd_product_id: (string | number) | null;
            recommend_materials: unknown[];
            attributes?: unknown | null;
            client_version?: unknown | null;
            brand_act: boolean;
        } | null;
        people_num: (string | number) | null;
        version: number;
    };
    code: number;
    message?: string | undefined;
}
