import { Action } from '@wosai/smart-mp-ui/dialog/dialog'

type DialogContext =
  | WechatMiniprogram.Page.TrivialInstance
  | WechatMiniprogram.Component.TrivialInstance

declare global {
  namespace SmartMpUI {
    export interface DialogOptions {
      lang?: string
      show?: boolean
      title?: string
      width?: string | number | null
      zIndex?: number
      theme?: string
      context?: (() => DialogContext) | DialogContext
      message?: string
      overlay?: boolean
      selector?: string
      ariaLabel?: string
      /**
       * @deprecated use custom-class instead
       */
      className?: string
      customStyle?: string
      transition?: string
      /**
       * @deprecated use beforeClose instead
       */
      asyncClose?: boolean
      beforeClose?: null | ((action: Action) => Promise<void | boolean> | void)
      businessId?: number
      sessionFrom?: string
      overlayStyle?: string
      appParameter?: string
      messageAlign?: string
      sendMessageImg?: string
      showMessageCard?: boolean
      sendMessagePath?: string
      sendMessageTitle?: string
      confirmButtonText?: string
      cancelButtonText?: string
      showConfirmButton?: boolean
      showCancelButton?: boolean
      closeOnClickOverlay?: boolean
      confirmButtonOpenType?: string
      type?: 'confirm' | 'alert'
    }

    export interface NotifyOptions {
      type?: 'primary' | 'success' | 'danger' | 'warning'
      color?: string
      zIndex?: number
      top?: number
      message: string
      context?: any
      duration?: number
      selector?: string
      background?: string
      safeAreaInsetTop?: boolean
      onClick?: () => void
      onOpened?: () => void
      onClose?: () => void
    }

    interface ToastOptions {
      show?: boolean
      type?: 'loading' | 'success' | 'fail' | 'html' | 'sqbLoading'
      mask?: boolean
      zIndex?: number
      context?: (() => ToastContext) | ToastContext
      position?: string
      duration?: number
      selector?: string
      forbidClick?: boolean
      loadingType?: string
      message?: ToastMessage
      onClose?: () => void
    }
  }
}
