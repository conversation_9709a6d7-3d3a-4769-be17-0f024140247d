export interface IFetchMyOrderResp {
    data: {
        storeName: string;
        storeId: string;
        merchantId: string;
        clientSn: string | null;
        payway: number;
        subPayway: number;
        orderType: number;
        merchantActivitySn: string;
        sn: string;
        subscribeOrderSn: string;
        userId: string;
        channelUserId: string;
        weixinAppId: string;
        totalDiscount: number;
        originalAmount: number;
        netAmount: number;
        ctime: number;
        status: string;
        redeemResult: {
            totalDiscount: number;
            merchantRedeemDetail: {
                activityId: string | null;
                activityIds: string[] | null;
                discountAmount: number;
                activitySn: string | null;
                name: string | null;
                message: string;
                subType: string | null;
                type: string | null;
                originalType: string | null;
            };
            systemRedeemDetail: {
                activityId: string | null;
                activityIds: string[] | null;
                discountAmount: number;
                activitySn: string | null;
                name: string | null;
                message: string;
                subType: string | null;
                type: string | null;
                originalType: string | null;
            };
        };
        goodsDetail?: unknown | null;
        userName: string;
        avatarUrl: string | null;
        mealPreparationStatus?: unknown | null;
    }[];
    code: number;
    message?: string | undefined;
}
