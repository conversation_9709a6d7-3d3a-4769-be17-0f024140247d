export interface IFetchPayload {
    discount_params: {
        sub_payway: number;
        payway: number;
        recharge_and_pay: boolean;
        mk_custom_info: {
            previous_pay_way?: any | null;
            gift_card_share_flag: boolean;
            gift_card_overlay_flag?: any | null;
            specific_goods_card_list: any[];
            previous_biz_id?: string | undefined;
        };
    };
    preset_time: number;
    order_type: string;
    packed: boolean;
    api_version: number;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
