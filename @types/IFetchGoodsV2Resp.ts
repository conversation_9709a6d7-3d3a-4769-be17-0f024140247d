export interface IFetchGoodsV2Resp {
    data: {
        total: number;
        goods: {
            category_id: string;
            items: {
                uuid: string;
                id: string;
                category_id: string;
                name: string;
                sale_times: {
                    start_time: string;
                    end_time: string;
                    intime_section: boolean;
                }[] | null;
                has_material: boolean;
                need_choose_spec: boolean;
                price: number;
                activity_price: number | null;
                gift_card_discount_price_text: string;
                description: string | null;
                photo_url: string | null;
                unit: string;
                unit_type: number;
                sku: number | null;
                out_of_stock: boolean;
                sale_count: number;
                hot_sale_seq: number | null;
                min_sale_num: number | null;
                item_tag: number | null;
                item_tags: unknown[] | null;
                discount_texts: {
                    discount_text: string;
                    quota_count: number | null;
                }[] | null;
                discount_text: string | null;
                quota_count: number | null;
                discount_type: number | null;
                sale_time: {
                    type: number;
                    start_date: number | null;
                    end_date: number | null;
                    cycle: number[];
                    times: {
                        start_time: string;
                        end_time: string;
                    }[];
                };
                multiple: boolean;
                package: boolean;
                hot_sale: boolean;
            }[];
        }[];
    };
    code: number;
    message?: string | undefined;
}
