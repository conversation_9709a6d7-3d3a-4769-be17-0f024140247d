export interface IFetchAddressLine {
    from: {
        lat: string | null;
        lon: string | null;
    };
    to: {
        lat: string | null;
        lon: string | null;
    };
    geoType: string;
    directionType: string;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
