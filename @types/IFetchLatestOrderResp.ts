export interface IFetchLatestOrderResp {
    data: {
        campus_order: boolean;
        order_campus_delivery?: unknown | null;
        merchant_discount_name?: unknown | null;
        merchant_activity_sn: (string | number) | null;
        merchant_activity_type?: unknown | null;
        buyer_uid: string;
        buyer_login: string;
        store_name: string;
        terminal_id: string;
        qr_code_name: string;
        qr_code_type?: unknown | null;
        items: {
            ctime: (string | number) | null;
            merchant_id: (string | number) | null;
            store_id: (string | number) | null;
            spec: {
                name: string;
                price: number;
                id: string;
            };
            order_time?: unknown | null;
            item_uid: string;
            extra_info: {
                unit_type: string;
                local_goods_id: string;
                category_sort: number;
                sale_weight: number;
                item_sort: number;
                origin_goods_price: number;
            };
            item: {
                category_id: string;
                url: string;
                name: string;
                total_amount: number;
                sku?: unknown | null;
                attached_info: string;
                attached_info_without_materials: string;
                discount_number: number;
                display_order?: unknown | null;
                photo_url: string;
                spu_type: string;
                category_sort?: unknown | null;
                min_sale_num: (string | number) | null;
                item_tag: number;
                unit: string;
                status: number;
                discount_price: number;
                number: number;
                out_of_stock?: unknown | null;
                is_multiple: (boolean | number) | null;
                unit_type: number;
                id: string;
                weight: (string | number) | null;
                return_number: number;
                price: number;
                must?: unknown | null;
                number_decimal: number;
            };
            service_type: number;
            user_icon: string | null;
            package_group_name: string | null;
            quota_count?: unknown | null;
            pack_fee: (string | number) | null;
            order_sn: (string | number) | null;
            gift_food: boolean;
            table_id: (string | number) | null;
            tag_name: string | null;
            manual_change_price: boolean;
            brand_act_product?: unknown | null;
            sub_payway?: unknown | null;
            payway?: unknown | null;
            category_id: (string | number) | null;
            cover: boolean;
            client_version: number;
            materials?: unknown | null;
            brand_act: boolean;
            discount_strategy?: unknown | null;
            meal_type: string;
            mk_custom_info?: unknown | null;
            activity_name: string | null;
            compatible?: unknown | null;
            from_cart: boolean;
            user_name: string | null;
            package_group_id?: unknown | null;
            package_items?: unknown | null;
            open_table_must_order: boolean;
            open_table_item_editable: boolean;
            people_num: (string | number) | null;
            terminal_sn: (string | number) | null;
            recommend_materials?: unknown | null;
            id: (string | number) | null;
            attributes: {
                name: string;
                title: string | null;
                seq?: unknown | null;
                id: (string | number) | null;
            }[];
            process_status: string;
            must?: unknown | null;
            number: number;
        }[];
        trade_no: string;
        remark: string | null;
        extra: {
            cellphone: string | null;
            campus_delivery: string | null;
            pre_reduce_no: string | null;
            hb_fq: string | null;
            delivery_info: string | null;
            store_name: string;
            campus_station: string | null;
            dm_info: string | null;
            book_order_info_dto: string | null;
            wx_trace_id: string | null;
            mp_scene: string | null;
        };
        status: number;
        compatible: boolean;
        deleted: boolean;
        sub_payway: number;
        payway: number;
        redeem_details?: unknown | null;
        sqb_trans_sn: string;
        order_campus_station: {
            ctime: (string | number) | null;
            cellphone: (string | number) | null;
            user_id: (string | number) | null;
            name: string | null;
            order_sn: string;
            mtime: (string | number) | null;
            address?: unknown | null;
            gender: (string | number) | null;
            version: (string | number) | null;
            latitude: (string | number) | null;
            station_id: (string | number) | null;
            longitude: (string | number) | null;
            campus_id: number;
            id: (string | number) | null;
        };
        station_address?: unknown | null;
        campus_exception?: unknown | null;
        pay_time: number;
        refresh_on_mini: boolean;
        cancel_enabled: boolean;
        refund_revoke_enabled: boolean;
        refund_apply?: unknown | null;
        wx_goods?: unknown | null;
        membership_list?: unknown | null;
        trans_sn: string;
        cashier_biz_params?: unknown | null;
        goods_batch_infos?: unknown | null;
        order_seq: string;
        allow_card_pay: boolean;
        version: number;
        delivery_floor: string;
        process_status: string;
        ctime: number;
        merchant_id: string;
        subject?: unknown | null;
        store_id: string;
        pack_amount: number;
        discount_amount?: unknown | null;
        merchant_discount: number;
        qr_code: string;
        client_sn: string;
        cashier_mode: boolean;
        table_no: string;
        area_id?: unknown | null;
        book_order: boolean;
        book_time?: unknown | null;
        refund_apply_enabled: boolean;
        client_tracking_data?: unknown | null;
        order_track_status?: unknown | null;
        invoice: {
            url: string | null;
            text: string | null;
            show: boolean;
        };
        pay_controll?: unknown | null;
        pay_channel_list?: unknown | null;
        refund_amount: number;
        table_id: string;
        total_discount: number;
        packed: boolean;
        terminal_sn: (string | number) | null;
        extra_info: {
            orderSource: string;
            settleTime: number;
            orderInCampus: boolean;
            deliveryErrorCode: string;
            acceptTime: number;
            deliveryFeeDetail?: {} | undefined;
            weixinAppId: string;
            brandActivity: boolean;
            sqbScene: string;
            membership: boolean;
            upayProfitSharing: {
                charge: {
                    charge_flag: string;
                    charge_params: {
                        chargeType: number;
                        freeOrderAmount: number;
                        merchantMpActivity: boolean;
                        ufoodActivity: boolean;
                        freeOrderCount: number;
                        diningHallActivity: boolean;
                        ufoodFreeFeeActivity: boolean;
                    };
                }[];
                sharing_flag: string;
                sharing_notify_url: string;
            };
            isDeliveryConvert: boolean;
            sqbBizModel: string[];
            areaId: string;
            channelUserId: string;
            preReduceNo: string;
            deliveryFloor: string;
            deliveryErrorMsg: string;
            tradeAppId: string;
            amountComposition: {
                compositionItems: {
                    amount: string;
                    category: string;
                }[];
            };
        };
        id: string;
        type: string;
        sn: string;
        mtime: number;
        original_amount: number;
        effective_amount: number;
        profit_sharing_amount?: unknown | null;
        receive_amount: number;
        buyer_pay_amount: number;
        order_tag: number;
    };
    code: number;
    message?: string | undefined;
}
