export interface IFetchAddressLineResp {
    data: {
        status: number;
        message: string;
        request_id: string;
        result?: {
            routes: {
                mode: string;
                distance: number;
                duration: number;
                direction: string;
                polyline: number[];
                steps: {
                    instruction: string;
                    polyline_idx: number[];
                    road_name: string;
                    dir_desc: string;
                    distance: number;
                    act_desc: string;
                    type: number;
                }[];
            }[];
        } | undefined;
    };
    code: number;
    message?: string | undefined;
}
