export interface IAddAndRedeemBatch {
    compatible: boolean;
    store_id: string;
    merchant_id: string;
    merchantId: string;
    storeId: string;
    items: {
        client_version: number;
        payway: number;
        sub_payway: number;
        store_id: string;
        merchant_id: string;
        service_type: number;
        discount_strategy: string;
        service_type_name: string;
        from_cart: boolean;
        cover: boolean;
        compatible: boolean;
        meal_type: string;
        mk_custom_info: {
            previous_pay_way?: any | null;
            gift_card_share_flag: boolean;
            gift_card_overlay_flag?: any | null;
            specific_goods_card_list: any[];
            previous_biz_id?: string | undefined;
        };
        number: number;
        item: {
            name: string;
            price: number;
            category_id: string;
            id: string;
            number: number;
        };
        item_uid?: string | undefined;
    }[];
    mk_custom_info: {
        previous_pay_way?: any | null;
        gift_card_share_flag: boolean;
        gift_card_overlay_flag?: any | null;
        specific_goods_card_list: any[];
        previous_biz_id?: string | undefined;
    };
}
