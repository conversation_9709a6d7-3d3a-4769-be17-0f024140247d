export interface IGetEntranceResp {
    data: {
        PLUGIN_IS_SHOW_AD_ON_ORDER_DETAIL: boolean;
        PLUGIN_GOODS_VIEWPORT_OFFSET: number;
        PLUGIN_GOODS_PAGE_SIZE: number;
        PLUGIN_GOODS_SCROLL_DEBOUNCE: number;
        ADSENSE_SHOW_IN_ALIPAY: boolean;
        ADSENSE_SHOW_IN_WECHAT: boolean;
        AUTH_MERCHANT_LIST: string[];
        AUTH_ON: boolean;
        PRE: string;
        MIANGUANGGAO: string;
        ALIPAY_PLUGIN: boolean;
        DEBUG: boolean;
        ACTIVITY_STATUS_VALID: string;
        ALIPAY_BENEFIT: number;
        SLS_ENABLE: boolean;
        SLS_SEND_LOGS_TIMEOUT: number;
        SENTRY_ENABLE: boolean;
        SLS_WHITE_LIST: string[];
        SENTRY_WHITE_LIST: string[];
        QRCODE_MAPS: {
            [x: string]: string;
        };
        BUTTONS: {
            name: string;
            type: string;
            url: string;
            method: string | null;
            confirm: string[] | null;
            success: string | null;
            pluginName: string | null;
            componentName: string | null;
        }[];
        SQB_FLS_INFO: {
            name: string;
            desc: string;
            image: string;
        } | null;
        TAKEOUT_WECHAT_LINK: string | null;
    };
    code: number;
    message?: string | undefined;
}
