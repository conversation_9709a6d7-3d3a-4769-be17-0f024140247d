export interface IGoodsDetailResp {
    data: {
        item: {
            id: string;
            name: string;
            photo_url?: string | undefined;
            description: string | null;
            display_order?: number | undefined;
            valid?: any | null;
            category_id: string;
            merchant_id: string;
            store_id: string;
            price: number;
            activity_price: number | null;
            gift_card_discount_price: number | null;
            discount_prices: {
                can_enjoy: boolean;
                discount_type: string;
                discount_price: number;
                quota_count: number;
                discount?: any | null;
            }[];
            discount_texts: {
                discount_text: string;
                quota_count: number;
            }[];
            second_activity_discount: number | null;
            quota_count?: any | null;
            for_sale: boolean;
            out_of_stock: boolean;
            sku?: any | null;
            sku_default?: any | null;
            reset_sku: boolean;
            unit: string;
            unit_type: number;
            category_name: string;
            category_sort?: any | null;
            spu_type: string;
            is_multiple: number;
            item_tag?: any | null;
            add_count?: any | null;
            last30_days_sale_count?: any | null;
            hotsale_seq?: any | null;
            hotsale_product: boolean;
            keep_price_equal: boolean;
            arrival_price: number;
            takeout_price: number;
            min_sale_num: number | null;
            sale_times?: any | null;
            barcode?: any | null;
            sale_terminals: number[];
            custom_out_of_stock: boolean;
            handled_add_count?: number | undefined;
            number: number;
        };
        specs: {
            title: string;
            options: {
                id: string;
                name: string;
                price: number;
                seq: number;
                activity_price?: unknown | null;
                quota_count?: unknown | null;
                second_activity_discount?: unknown | null;
                discount_prices?: unknown | null;
                discount_texts?: unknown | null;
                arrival_price: number;
                takeout_price: number;
            }[];
        };
        attributes: {
            name: string;
            title: string | null;
            seq?: unknown | null;
            id: (string | number) | null;
        }[];
        material_ids?: unknown | null;
        materials?: unknown | null;
        item_tags?: unknown | null;
        material_groups: unknown[];
        ingredient_names?: unknown | null;
        package_must_order_products?: unknown | null;
        package_optional_groups?: unknown | null;
        bought_times?: unknown | null;
        latest_buy_time?: unknown | null;
        sale_time: {
            type: number;
            start_date: number | null;
            end_date: number | null;
            cycle: number[];
            times: {
                start_time: string;
                end_time: string;
            }[];
        };
    };
    code: number;
    message?: string | undefined;
}
