export interface IGetStoreHBFQConfigResp {
    data: {
        vendor_id: string;
        merchant_id: string;
        merchant_sn: string;
        merchant_name: string;
        merchant_country: string;
        currency: string;
        longitude: string;
        latitude: string;
        district_code: string;
        store_id: string;
        store_sn: string;
        store_client_sn: string | null;
        store_name: string;
        store_city: string;
        clearance_provider: number;
        pay_status: number;
        sharing_switch: number;
        common_switch: string;
        merchant_daily_max_sum_of_trans: number;
        merchant_daily_max_credit_limit_trans: number | null;
        merchant_monthly_max_credit_limit_trans: number | null;
        union_over_seas_wallet_single_tran_limit: number;
        union_over_seas_wallet_day_tran_limit: number;
        payway_day_credit_limits?: unknown | null;
        payway_month_credit_limits?: unknown | null;
        merchant_bankcard_single_max_limit: number;
        merchant_bankcard_day_max_limit: number;
        merchant_bankcard_month_max_limit: number;
        merchant_daily_payway_max_sum_of_trans?: any | null;
        merchant_single_max_of_tran?: unknown | null;
        credit_pay?: unknown | null;
        use_client_store_sn?: unknown | null;
        deposit?: unknown | null;
        is_sent_store_id: boolean;
        is_need_refund_fee_flag?: unknown | null;
        hit_payway?: unknown | null;
        alipay_huabei_status: number;
        alipay_huabei_limit: number;
        alipay_huabei_params: {
            hb_fq_seller_percent: string;
            hb_fq_num: string;
            seller_fee_rate: number;
            buyer_fee_rate: number;
        }[];
        alipay_credit_params: {
            fq_seller_percent: string;
            fq_num: string;
            seller_fee_rate: number;
            buyer_fee_rate: number;
        }[];
        provider: number;
        lkl_up_trade_params: {
            app_id: string;
            cert_id: string;
            sys_pid: string;
            category: string | null;
            fee_rate: string;
            sign_type: string;
            public_key: string;
            service_id: string | null;
            merchant_name: string;
            up_private_key: string;
            alipay_store_id: string | null;
            provider_mch_id: string;
            alipay_sub_mch_id: string;
            liquidation_next_day: boolean;
            fee_rate_tag: {
                "4": string;
            };
        };
        term_info: {
            term_id: string;
            term_type: string | null;
            serial_num: string | null;
        };
        term_id: string;
        channel_name: string;
        trade_app: string;
    };
    code: number;
    message?: string | undefined;
}
