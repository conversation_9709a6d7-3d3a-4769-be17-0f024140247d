export interface IGetDefaultAddressV2Resp {
    data: {
        address: string;
        addressCode?: string | undefined;
        addressId: number;
        address_name?: string | undefined;
        appVersion: number;
        areaId: number;
        areaName: string | null;
        buildingNumber?: string | undefined;
        campusId: number;
        campusName: string | null;
        cellphone: string;
        city: string;
        cityCode: string;
        deliveryFee: (string | number) | null;
        distance: number;
        district: string;
        districtCode: string;
        floor?: string | undefined;
        gender: number;
        houseNumber: string;
        id: string;
        invalidReason: string | null;
        isDefault: boolean;
        latitude: string;
        longitude: string;
        preChooseFloor: (string | number) | null;
        presetTime: number;
        province: string;
        provinceCode: string;
        type: number;
        userId: string;
        userName: string;
        valid: boolean;
    };
    code: number;
    message?: string | undefined;
}
