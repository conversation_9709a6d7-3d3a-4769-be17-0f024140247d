export interface IGetRefundGoodsResp {
    data: {
        price: number;
        gift_food: boolean;
        discount_price: number;
        attach_info?: any | null;
        main_image: string;
        open_table_must_order: boolean;
        refund_count: number;
        goods_process_status: string;
        name: string;
        id: string;
        count: number;
    }[];
    code: number;
    message?: string | undefined;
}
