export interface IInitOrderV2Resp {
    data: {
        order_main: {
            sn: string;
            order_type: string;
            status: string;
            original_amount: number;
            effective_amount: number;
            need_pay_amount: number;
            un_discountable_amount: number;
            merchant_discount_total_amount: number;
            coupon_discount_amount: number;
            bonus_point_amount: number;
            activity_discount_amount: number;
            temp_discount_amount: number;
            vip_discount_amount: number;
            eliminate_amount: number;
            product_discount_amount: number;
            channel_assist_discount: number;
            buyer_pay_amount: number;
            receive_amount: number;
            user_id: string;
            user_name: string | null;
            avatar_url: string | null;
            refund_amount: number;
            goods_count: number;
            table_id: string;
            table_no: string;
            order_tag: number;
            payment_channels: string | null;
            pay_time: number;
            pack_amount: string | null;
            order_seq: string;
            subject: string | null;
            items_info: {
                spu_title: string;
                sku_title: string | null;
                price: number;
                sale_count: number;
                id: string;
                name: string;
                value?: any | null;
                number: number;
            }[];
            remark: string | null;
            merchant_id: string;
            merchant_name: string | null;
            merchant_sn: string;
            store_id: string;
            store_sn: string;
            store_name: string;
            cashier_id: string | null;
            cashier_name: string | null;
            packed: string;
            deleted: string;
            terminal_id: string | null;
            terminal_sn: string | null;
            terminal_name: string | null;
            terminal_type: string | null;
            deliver_type: string | null;
            deliver_amount: string | null;
            profit_sharing_amount: string | null;
            order_source: string;
            ctime: number;
            mtime: number;
            version: number;
            extra: {
                people_num: (string | number) | null;
                areaId: string;
                user_name: string;
                open_table_time: string;
                initialAppId: string;
                user_icon: string;
                initialChannelUserId: string;
                acceptTime: number;
            };
            is_final: string | null;
            id: string;
            campus_order: string | null;
            campus_id: string | null;
            campus_delivery_fee: string | null;
            goods_list: {
                id: string;
                order_sn: string;
                category_id: string;
                spu_id: string;
                spu_title: string;
                spu_type: string;
                main_image_url: string | null;
                sku_id: string | null;
                sku_title: string | null;
                sku_type: string;
                activity_id: string | null;
                activity_type: string | null;
                discount_amount: number;
                merchant_discount_share_amount: number | null;
                refund_amount: number;
                ref_pay_type: string;
                has_processed_stock: string;
                process_status: string;
                batch_no: string;
                attached_info?: unknown | null;
                attribute_infos?: unknown | null;
                materials?: unknown | null;
                total_amount: number | null;
                discount_count: number | null;
                package_goods?: unknown | null;
                count: number;
                origin_sale_price: number;
                now_discount_price: number;
                goods_discount_type?: unknown | null;
                refund_count: number | null;
                ref_goods_id: string | null;
                ref_client_sn: string | null;
                sale_unit: string;
                unit_type: string;
                extra_info: {
                    local_goods_id: string;
                    user_name: string;
                    item_sort: number;
                    order_time_stamp: string;
                    user_icon: string;
                    category_sort: number;
                    unit_type: string;
                    origin_goods_price: number;
                    sale_weight: number;
                };
                item_snapshot?: unknown | null;
                version: string | null;
                goods_tag: number;
                ctime: number;
                mtime: number;
                spec?: unknown | null;
                goods_redeem_result_v2?: unknown | null;
            }[];
            item_snapshots?: unknown | null;
            pays_list?: unknown | null;
            redeems?: unknown | null;
            delivery_info?: unknown | null;
            order_address?: unknown | null;
            book_order_info_dto?: unknown | null;
            ever_paid: boolean;
            paid_or_with_redeem: boolean;
        };
        pre_create_order_vo?: unknown | null;
        cart_check_result?: unknown | null;
        pay_result?: unknown | null;
    };
    code: number;
    message?: string | undefined;
}
