export interface IGetShareMessageResp {
    data: {
        template_detail: {
            flag: string;
            moduleBuildId: number;
            hasBusinessConfig: boolean;
            moduleKey: string;
            boxStyle: {
                height: string;
            };
            rank: number;
            moduleId: number;
            component: string;
            slots: {
                modules: {
                    flag: string;
                    moduleBuildId: number;
                    hasBusinessConfig: boolean;
                    moduleKey: string;
                    boxStyle: {
                        height: string;
                    };
                    rank: number;
                    moduleId: number;
                    component: string;
                    name: string;
                    config?: {} | undefined;
                }[];
                nav: {
                    flag: string;
                    moduleBuildId: number;
                    hasBusinessConfig: boolean;
                    moduleKey: string;
                    boxStyle: {
                        height: string;
                    };
                    rank: number;
                    moduleId: number;
                    component: string;
                    name: string;
                    config?: {} | undefined;
                };
            };
            name: string;
            config?: {} | undefined;
        };
        template_id: number;
        template_name: string;
        store_id: string;
        pageInfo: {
            config_name: string | null;
            source_path: string | null;
            source_type: string | null;
            remark: string;
            url: string;
            params?: unknown | null;
            extend?: unknown | null;
            status: number;
            product_group_id: string;
            path: string | null;
            type: number;
            id: number;
            page_id: string;
            name: string;
            dev_code: string;
            source?: unknown | null;
        };
        page_id: string;
        product_id: string | null;
        theme_id: string | null;
        theme_detail?: unknown | null;
    };
    code: number;
    message?: string | undefined;
}
