export interface IFetchExtraResp {
    data: {
        planContent: {
            records: unknown[];
            total: number;
        };
        wxMchId: string;
        wxStoreId: string;
        wxGoodsActs: string;
        duration: {
            aop: {
                cost: number;
                start: number;
                end: number;
            };
            wxMchInfo: {
                cost: number;
                start: number;
                end: number;
            };
        };
        wxSubMchId: string;
        payConfig: string;
        wxProducts: string;
    };
    code: number;
    message?: string | undefined;
}
