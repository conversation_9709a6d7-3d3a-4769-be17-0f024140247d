export interface IRoundAddGood {
    table_id?: (number | string) | undefined;
    item_uid?: string | undefined;
    category_id?: string | undefined;
    service_type?: number | undefined;
    item?: {
        id: string;
        name: string;
        photo_url?: string | undefined;
        description: string | null;
        display_order?: number | undefined;
        valid?: any | null;
        category_id: string;
        merchant_id: string;
        store_id: string;
        price: number;
        activity_price: number | null;
        gift_card_discount_price: number | null;
        discount_prices: {
            can_enjoy: boolean;
            discount_type: string;
            discount_price: number;
            quota_count: number;
            discount?: any | null;
        }[];
        discount_texts: {
            discount_text: string;
            quota_count: number;
        }[];
        second_activity_discount: number | null;
        quota_count?: any | null;
        for_sale: boolean;
        out_of_stock: boolean;
        sku?: any | null;
        sku_default?: any | null;
        reset_sku: boolean;
        unit: string;
        unit_type: number;
        category_name: string;
        category_sort?: any | null;
        spu_type: string;
        is_multiple: number;
        item_tag?: any | null;
        add_count?: any | null;
        last30_days_sale_count?: any | null;
        hotsale_seq?: any | null;
        hotsale_product: boolean;
        keep_price_equal: boolean;
        arrival_price: number;
        takeout_price: number;
        min_sale_num: number | null;
        sale_times?: any | null;
        barcode?: any | null;
        sale_terminals: number[];
        custom_out_of_stock: boolean;
        handled_add_count?: number | undefined;
        number: number;
    } | undefined;
    specs?: any | null;
    attributes?: {
        name: string;
        title: string | null;
        seq?: unknown | null;
        id: (string | number) | null;
    } | undefined;
    material_ids?: any | null;
    materials?: any | null;
    item_tags?: any | null;
    material_groups?: any[] | undefined;
    ingredient_names?: any | null;
    bought_times?: any | null;
    latest_buy_time?: any | null;
    sale_time: {
        type: number;
        start_date: number | null;
        end_date: number | null;
        cycle: number[];
        times: {
            start_time: string;
            end_time: string;
        }[];
    };
    photo_url_list: string[];
    package_items?: {
        item: {
            id: string;
            name: string;
            photo_url?: string | undefined;
            description: string | null;
            display_order?: number | undefined;
            valid?: any | null;
            category_id: string;
            merchant_id: string;
            store_id: string;
            price: number;
            activity_price: number | null;
            gift_card_discount_price: number | null;
            discount_prices: {
                can_enjoy: boolean;
                discount_type: string;
                discount_price: number;
                quota_count: number;
                discount?: any | null;
            }[];
            discount_texts: {
                discount_text: string;
                quota_count: number;
            }[];
            second_activity_discount: number | null;
            quota_count?: any | null;
            for_sale: boolean;
            out_of_stock: boolean;
            sku?: any | null;
            sku_default?: any | null;
            reset_sku: boolean;
            unit: string;
            unit_type: number;
            category_name: string;
            category_sort?: any | null;
            spu_type: string;
            is_multiple: number;
            item_tag?: any | null;
            add_count?: any | null;
            last30_days_sale_count?: any | null;
            hotsale_seq?: any | null;
            hotsale_product: boolean;
            keep_price_equal: boolean;
            arrival_price: number;
            takeout_price: number;
            min_sale_num: number | null;
            sale_times?: any | null;
            barcode?: any | null;
            sale_terminals: number[];
            custom_out_of_stock: boolean;
            handled_add_count?: number | undefined;
            number: number;
        };
        attributes?: {
            name: string;
            title: string | null;
            seq?: unknown | null;
            id: (string | number) | null;
        }[] | undefined;
        material_ids?: any | null;
        materials?: any | null;
        item_tags?: any | null;
        material_groups?: any[] | undefined;
        ingredient_names?: any | null;
        package_must_order_products?: any | null;
        package_optional_groups?: any | null;
        bought_times?: any | null;
        latest_buy_time?: any | null;
        sale_time?: {
            type: number;
            start_date: number | null;
            end_date: number | null;
            cycle: number[];
            times: {
                start_time: string;
                end_time: string;
            }[];
        } | undefined;
        package_group_id?: string | undefined;
        package_group_name?: string | undefined;
        listIndex?: number | undefined;
        groupId?: string | undefined;
        index?: number | undefined;
        selectedAttributes?: number[][] | undefined;
        spec?: {
            id: string;
            name: string;
            price: number;
            seq: number;
            activity_price?: unknown | null;
            quota_count?: unknown | null;
            second_activity_discount?: unknown | null;
            discount_prices?: unknown | null;
            discount_texts?: unknown | null;
            arrival_price: number;
            takeout_price: number;
        } | undefined;
    }[] | undefined;
    user_icon?: string | undefined;
    user_name?: string | undefined;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
