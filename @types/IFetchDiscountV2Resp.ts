export interface IFetchDiscountV2Resp {
    data: {
        total_discount: number;
        share_flag: boolean;
        overlay_flag?: unknown | null;
        merchant_redeem_detail: {
            name: string;
        } | null;
        system_redeem_detail: {
            name: string;
        } | null;
        user_id: (string | number) | null;
        ctime: (string | number) | null;
        merchant_id: (string | number) | null;
        store_id: (string | number) | null;
        redeem_details: unknown[];
        mk_custom_info: {
            cart_expire_time: number;
            is_coupon_list_available: boolean;
        };
        redeem_digest?: unknown | null;
        alipay_redeem_detail?: {
            name: string;
        } | undefined;
    };
    code: number;
    message?: string | undefined;
}
