export interface IGetNearbyCampusListResp {
    data: {
        id: number;
        campusBizId: string;
        campusName: string;
        campusPageName: string;
        campusZoneId: number;
        campusZoneBizId: string;
        campusZoneName: string;
        province: string;
        city: string;
        district: string;
        provinceCode: string;
        cityCode: string;
        districtCode: string;
        latitude: string;
        longitude: string;
        address: string;
        hotSaleShow: number;
        announcement: string | null;
        banners: {
            bannerUrl: string;
            jumpUrl: string | null;
            status: number;
            terminal: number;
            title: string | null;
            pcId: string | null;
        }[];
        banner: string | null;
        bannerUrl: string | null;
        cover: string;
        status: number;
        thirdDeliveryType: string | null;
        outsideDeliveryType: string | null;
        deliveryType: string | null;
        deliveryRange: string | null;
        fence: string;
        inFence: boolean;
        ctime: number;
        storeId: string | null;
        campusOperateToolType: string | null;
    }[];
    code: number;
    message?: string | undefined;
}
