export interface IGatherIndexResp {
    activity: {
        globalDiscount?: unknown | null;
        collectionDiscount?: unknown | null;
        timeDiscount: unknown[];
        priceBreak?: unknown | null;
        cardDiscounts: unknown[];
        returnCardDiscount?: unknown | null;
        goodsCoupons: unknown[];
        membershipCard: unknown[];
        combinedActivities: unknown[];
        storedCardActivity: {
            id: string;
            sn: string;
            status: number;
            type: number;
            storeId: string;
            depositRules: {
                depositAmountLevel: number;
                discountAmountLevel: number;
                discountCount: number;
            }[];
            storeIds: string[];
        };
        membershipActivities: unknown[];
        goodsCouponActivities?: unknown | null;
        membershipDays?: unknown | null;
        deliveryActivities: {
            storeId: string;
            discount: number;
            activityId: string;
            activitySn: string;
            isSub: boolean;
            mode: string;
            goodsCouponFlag: boolean;
        }[];
        secondActivities: {
            discount: number;
            activityId: string;
            startDate: number;
            endDate: number;
            isSub: boolean;
            ctime: number;
            mode: string;
            goodsCouponFlag: boolean;
            activityName: string;
            dateOption: number;
            designateDate: unknown[];
            designateTime: unknown[];
            useCase: number[];
        }[];
        singleActivities: {
            activityId: string;
            startDate: number;
            endDate: number;
            isSub: boolean;
            ctime: number;
            mode: string;
            goodsCouponFlag: boolean;
            quotaOption: number;
            activityName: string;
            dateOption: number;
            designateDate: unknown[];
            designateTime: unknown[];
            useCase: number[];
        }[];
        memberActivities: {
            globalDiscount?: unknown | null;
            goodsDiscount?: unknown | null;
        };
        goodsV2Coupons: unknown[];
        goodsCouponNum: number;
    };
    simplifyData: boolean;
    supportCardPay: string;
    goods: {
        total: number;
        pages: {
            uuid: string;
            id: string;
            category_id: string;
            name: string;
            sale_times: {
                start_time: string;
                end_time: string;
                intime_section: boolean;
            }[] | null;
            has_material: boolean;
            need_choose_spec: boolean;
            price: number;
            activity_price: number | null;
            gift_card_discount_price_text: string;
            description: string | null;
            photo_url: string | null;
            unit: string;
            unit_type: number;
            sku: number | null;
            out_of_stock: boolean;
            sale_count: number;
            hot_sale_seq: number | null;
            min_sale_num: number | null;
            item_tag: number | null;
            item_tags: unknown[] | null;
            discount_texts: {
                discount_text: string;
                quota_count: number | null;
            }[] | null;
            discount_text: string | null;
            quota_count: number | null;
            discount_type: number | null;
            sale_time: {
                type: number;
                start_date: number | null;
                end_date: number | null;
                cycle: number[];
                times: {
                    start_time: string;
                    end_time: string;
                }[];
            };
            multiple: boolean;
            package: boolean;
            hot_sale: boolean;
        }[][];
    };
    areas: {
        enabled: boolean;
        type?: unknown | null;
        areas?: unknown | null;
    };
    store: {
        collectionNums?: unknown | null;
        firstIndustry: string;
        firstIndustryCode: string;
        industry: string;
        industryCode: string;
        merchantName: string;
        merchantId: string;
        merchantSn: string;
        alipayStatus: number;
        wechatStatus: number;
        disabled: number;
        discounts: {
            globalDiscount?: unknown | null;
            collectionDiscount?: unknown | null;
            timeDiscount: unknown[];
            priceBreak?: unknown | null;
            priceBreaks: unknown[];
            priceBreakCards: unknown[];
            returnCards: unknown[];
            membershipCards: unknown[];
            singleActivity: number;
            secondDiscounts: {
                discount: number;
                timeBuckets?: unknown | null;
                daysOfWeek: unknown[];
                daysOfMonth: unknown[];
                orderTypes: string[];
            }[];
        };
        discount?: unknown | null;
        organizationPath?: unknown | null;
        categoriesNames?: unknown | null;
        coupons: unknown[];
        storeId: string;
        storeName: string;
        storeExtraName: string;
        contactPhone: string;
        slogan: string;
        payWay: number;
        categoryId?: unknown | null;
        location: {
            lat: string;
            lon: string;
        };
        storeAddress: {
            province: string;
            city: string;
            district: string;
            streetAddress: string;
            address: string;
            aoiName?: unknown | null;
            houseNumber: string;
            adcode?: unknown | null;
        };
        storePhoto: {
            innerPhotos: unknown[];
            outerPhotos: unknown[];
            goodsPhotos: unknown[];
            coverPhoto?: unknown | null;
            logoUrl?: unknown | null;
        };
        storeBasic: {
            averageConsumption?: unknown | null;
            storeArea?: unknown | null;
            businessHoursStart: string;
            businessHoursEnd: string;
        };
        onceOpened: boolean;
        storeType: number;
        storeSn: string;
    };
    mcc: {
        [x: string]: unknown;
    };
}
