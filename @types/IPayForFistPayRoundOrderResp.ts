export interface IPayForFistPayRoundOrderResp {
    data: {
        sn: string;
        trans_sn: string | null;
        client_sn: string;
        net_amount: number | null;
        status: string | null;
        total_amount: string;
        total_discount: number | null;
        operator: string | null;
        subject: string | null;
        qr_code: string | null;
        wap_pay_request: {
            time_stamp: string;
            pay_sign: string;
            app_id: string;
            sign_type: string;
            nonce_str: string;
            trade_no: string | null;
            redirect_url: string | null;
            scan_qr_code_content: string | null;
            encrypt_data: string | null;
            package: string;
        };
        has_risk: boolean;
        action: string | null;
        risk_id: string | null;
        cart_check_result?: unknown | null;
        acquiring?: unknown | null;
    };
    code: number;
    message?: string | undefined;
}
