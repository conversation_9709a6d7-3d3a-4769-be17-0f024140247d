export interface IMakeOrderAndPayForFistPayRound {
    table_id: number | string;
    pay_way: number;
    type: string;
    items: {
        item_uid: string;
        number: number;
    }[];
    terminal_sn: string;
    total_discount: number;
    redeem_digest: string | null;
    mk_custom_info: {
        previous_pay_way?: any | null;
        gift_card_share_flag: boolean;
        gift_card_overlay_flag?: any | null;
        specific_goods_card_list: any[];
        previous_biz_id?: string | undefined;
    };
    area_id: number;
    table_name: string;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
