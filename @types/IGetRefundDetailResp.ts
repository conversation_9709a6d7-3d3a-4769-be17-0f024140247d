export interface IGetRefundDetailResp {
    data: {
        apply_proof: unknown[];
        notified: string;
        processed: string;
        operate_log: {
            status: string;
            type: string;
            sub_message: string;
            seller_message: string;
            refund_records?: unknown | null;
            seller_sub_message: string;
            ctime: number;
            refund_batch_no?: unknown | null;
            message: string;
            apply_proof: unknown[];
        }[];
        refund_amount: number;
        id: number;
        expire_time: number;
        order_type: string;
        part_refund: boolean;
        mtime: number;
        refund_items: {
            name: string;
            attach_info: string | null;
            main_image: string;
            refund_amount: number;
            id: string;
            refund_count: number;
        }[];
        ctime: number;
        apply_reason: string;
        pack_amount: number;
        order_seq: string;
        user_id: string;
        merchant_id: string;
        delivery_amount: number;
        refund_type: string;
        store_id: string;
        apply_time: number;
        refund_time: number | null;
        reject_time: number | null;
        apply_status: string;
        refund_process_type: string | null;
        reject_reason: string | null;
        sn: string;
    };
    code: number;
    message?: string | undefined;
}
