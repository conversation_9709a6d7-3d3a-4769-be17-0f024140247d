export interface IGetOrderOrBatchDetailResp {
    data: {
        items: unknown[];
        redeem: {
            user_id: (string | number) | null;
            share_flag: boolean | null;
            overlay_flag: boolean | null;
            merchant_redeem_detail?: unknown | null;
            mk_custom_info: {
                is_coupon_list_available: boolean;
                cart_expire_time: number;
            };
            redeem_digest: string | null;
            system_redeem_detail?: unknown | null;
            ctime: (string | number) | null;
            total_discount: number;
            redeem_details: unknown[];
            store_id: (string | number) | null;
            merchant_id: (string | number) | null;
        };
        goods_batch_infos?: unknown | null;
        need_pay_amount: number;
        cart: {
            sn: string | null;
            people_num: number | null;
            version: number;
            total: number;
            total_price: number;
            records: {
                id: string;
                item_id: string;
                spec_id: string;
                name: string;
                attached_info: string;
                status: number;
                price: number;
                num: number;
                ctime: number;
                mtime: number;
                category_id: string;
                url: string;
                sku: string | null;
                out_of_stock: boolean;
                user_icons: string[];
                last_deal_type: string;
                last_add_num: number;
                user_id: string;
                user_name: string;
                user_icon: string;
                last_add: boolean;
                open_table_must_order: boolean;
                open_table_must_order_editable: boolean;
                spu_type: string;
                min_sale_num: number | null;
                display_order: number;
                category_sort: number;
                materials: unknown[];
                brand_acd_product_id: (string | number) | null;
                recommend_materials: unknown[];
                attributes?: unknown | null;
                client_version?: unknown | null;
                brand_act: boolean;
            }[];
            last_deal_record: {
                id: string;
                item_id: string;
                spec_id: string;
                name: string;
                attached_info: string;
                status: number;
                price: number;
                num: number;
                ctime: number;
                mtime: number;
                category_id: string;
                url: string;
                sku: string | null;
                out_of_stock: boolean;
                user_icons: string[];
                last_deal_type: string;
                last_add_num: number;
                user_id: string;
                user_name: string;
                user_icon: string;
                last_add: boolean;
                open_table_must_order: boolean;
                open_table_must_order_editable: boolean;
                spu_type: string;
                min_sale_num: number | null;
                display_order: number;
                category_sort: number;
                materials: unknown[];
                brand_acd_product_id: (string | number) | null;
                recommend_materials: unknown[];
                attributes?: unknown | null;
                client_version?: unknown | null;
                brand_act: boolean;
            };
            spu_count_map: {
                [x: string]: number;
            };
        };
        disable_redeem: boolean;
    };
    code: number;
    message?: string | undefined;
}
