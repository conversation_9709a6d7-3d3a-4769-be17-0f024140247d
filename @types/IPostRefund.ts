export interface IPostRefund {
    sn: string;
    goods: {
        id: string;
        name: string;
        attach_info?: unknown | null;
        main_image: string | null;
        refund_count: number;
    }[];
    proofs: unknown[];
    reason: string;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
