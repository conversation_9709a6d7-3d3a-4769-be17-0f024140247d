export interface IFetchDiscountV2 {
    meal_type: string;
    discount_strategy: string;
    from_cart: boolean;
    mk_custom_info: {
        previous_pay_way?: any | null;
        gift_card_share_flag: boolean;
        gift_card_overlay_flag?: any | null;
        specific_goods_card_list: any[];
        previous_biz_id?: string | undefined;
    };
    payway: number;
    recharge_and_pay: boolean;
    service_type_name: string;
    sub_payway: number;
    table_id: number | string;
    total_amount: number;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
