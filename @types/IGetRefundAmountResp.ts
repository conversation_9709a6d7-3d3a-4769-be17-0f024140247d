export interface IGetRefundAmountResp {
    data: {
        refund_text: string;
        deliver_amount: number;
        refund_pays: {
            ref_pay_order_no: string | null;
            ref_vip_order_no: string | null;
            ref_platform_order_no: string | null;
            origin_pays?: any | null;
            tag?: any | null;
            refund_amount: number;
            id: number;
            local_order_no: string | null;
            payment_channel: string;
            payment_channel_name: string;
            ref_local_order_no: string | null;
        }[];
        refund_amount: number;
        pack_amount: number;
        refund_goods: {
            main_image?: any | null;
            refund_count: number;
            discount_goods_type: string;
            name?: any | null;
            refund_amount: number;
            db_goods_id: string;
            id: string;
            attach_info?: any | null;
        }[];
    };
    code: number;
    message?: string | undefined;
}
