export interface IGetStoreListResp {
    data: {
        currentPage: number;
        pageSize: number;
        totalCount: number;
        totalPage: number;
        value: {
            score?: unknown | null;
            id: (string | number) | null;
            categoriesNames?: unknown | null;
            coupons: unknown[];
            merchantId: string;
            scanStatus: number;
            wechatActivityMessages: unknown[];
            alipayActivityMessages: unknown[];
            campusDelivery?: unknown | null;
            timeoutPay: number;
            contactPhone: string;
            streetAddress: string;
            houseNumber: string;
            deliveryTimesV2: {
                [x: string]: {
                    startTime: number;
                    endTime: number;
                }[];
            };
            discount?: unknown | null;
            storeSn: string;
            storeExtraName: string;
            alipayStatus: number;
            wechatStatus: number;
            collectionNums?: unknown | null;
            logoUrl: string;
            coverPhoto: string;
            industry: string;
            distance: number;
            industryCode: string;
            merchantName: string;
            businessHoursStart: string;
            businessHoursEnd: string;
            slogan: string;
            aoiName: string;
            appId: string;
            campuses: {
                deliveryType: number;
                timeoutPay: number;
                weight: number;
                campusId: number;
            }[];
            storeName: string;
            items?: unknown | null;
            merchantSn: string;
            location: string;
            takeoutStatus: number;
            discounts: {
                singleActivity: number;
                secondDiscounts: unknown[];
                globalDiscount?: unknown | null;
                collectionDiscount?: unknown | null;
                timeDiscount: unknown[];
                priceBreak?: unknown | null;
                priceBreaks: unknown[];
                priceBreakCards: {
                    discountType: number;
                    fills: number;
                    minus: number;
                    activitySn: string;
                    isEnable: boolean;
                }[];
                returnCards: unknown[];
                membershipCards: unknown[];
            };
            address: string;
            storeId: string;
        }[];
    };
    code: number;
    message?: string | undefined;
}
