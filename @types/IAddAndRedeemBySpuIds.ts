export interface IAddAndRedeemBySpuIds {
    discount_strategy: string;
    meal_type: string;
    mk_custom_info: {
        [x: string]: any;
    };
    payway: number;
    service_type: number;
    specific_items: {
        spu_id: string;
        sku_id: string | null;
    }[];
    sub_payway: number;
    user_icon: string;
    user_name: string;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
