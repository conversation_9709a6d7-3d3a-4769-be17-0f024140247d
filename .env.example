# 基础路径配置
# 平台支付宝
MY_ROOT_DIR=/Users/<USER>/Projects/WOSAI/FRONTEND/mp/qr-mini-pay-alipay-biz
# 平台久久折微信
WX_ROOT_DIR=/Users/<USER>/Projects/WOSAI/FRONTEND/mp/qr-mini-pay-jjz-biz
# smart-mp-dev 项目路径
SMART_DEV_ROOT_DIR=/Users/<USER>/Projects/WOSAI/FRONTEND/mp/smart-mp-biz/smart-mp-dev
SMART_DEV_ROOT_DIR_ALIPAY = /Users/<USER>/Projects/WOSAI/FRONTEND/mp/smart-mp-biz/smart-mp-dev-alipay

WORK_DIR=/Users/<USER>/Projects/WOSAI/FRONTEND/mp/smart-mp-biz/dist

# 包配置 (JSON 格式数组)
PACKAGES=[{"name":"center","alipayPath":"KAWNGVMGQZBZ/node_modules/@wosai/smart-mp-biz-center","wxPath":"KAWNGVMGQZBZ","source":"center"},{"name":"order","alipayPath":"VSL3RFWLLAOC/node_modules/@wosai/smart-mp-biz-order","wxPath":"VSL3RFWLLAOC","source":"order"},{"name":"home","alipayPath":"E1QDEFPQUJWB/node_modules/@wosai/smart-mp-biz-home","wxPath":"E1QDEFPQUJWB","source":"home"},{"name":"store","alipayPath":"GDLPKTFLUN7C/node_modules/@wosai/smart-mp-biz-store","wxPath":"GDLPKTFLUN7C","source":"store"},{"name":"purchased","alipayPath":"EZDGV4K8AG7K/node_modules/@wosai/smart-mp-biz-purchased","wxPath":"EZDGV4K8AG7K","source":"purchased"},{"name":"roundmeal","alipayPath":"JDLTQXMWBQMW/node_modules/@wosai/smart-mp-biz-roundmeal","wxPath":"JDLTQXMWBQMW","source":"roundmeal"}]

# 微信小程序包路径配置
WX_CENTER_PATH=KAWNGVMGQZBZ
WX_ORDER_PATH=VSL3RFWLLAOC
WX_ROUNDMEAL_PATH=JDLTQXMWBQMW
WX_HOME_PATH=E1QDEFPQUJWB
WX_STORE_PATH=GDLPKTFLUN7C
WX_PURCHASED_PATH=EZDGV4K8AG7K

# 工具配置
wx_cli=/Applications/wechatwebdevtools.app/Contents/MacOS/cli
# 是否自动构建微信小程序
AUTO_WX_BUILD=false
# 微信是否copy到miniprogram
WX_COPY_TO_MINIPROGRAM = false
