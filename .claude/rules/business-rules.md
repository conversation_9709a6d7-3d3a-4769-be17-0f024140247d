# 业务规范

## 小程序业务规范

### 平台适配规范
- **微信小程序**: 遵循微信小程序开发规范和审核要求
- **支付宝小程序**: 适配支付宝小程序API和UI规范
- **跨平台兼容**: 使用条件编译处理平台差异

### 用户体验规范
```typescript
// 加载状态管理
function showLoading(title: string = '加载中...') {
  wx.showLoading({ title })
}

function hideLoading() {
  wx.hideLoading()
}

// 错误提示规范
function showError(message: string) {
  wx.showToast({
    title: message,
    icon: 'error',
    duration: 2000
  })
}
```

### 数据流规范
- 使用统一的状态管理模式
- API响应数据结构标准化
- 缓存策略统一管理
- 数据验证和类型安全

## 商户业务规范

### 店铺信息管理
```typescript
interface IStoreInfo {
  storeId: string
  storeName: string
  storeStatus: 'active' | 'inactive' | 'suspended'
  serviceTypes: string[]
  location: {
    address: string
    coordinates: [number, number]
  }
}
```

### 订单业务规范
- 订单状态流转标准化
- 支付流程统一处理
- 退款逻辑规范化
- 订单数据完整性保证

### 权限控制规范
```typescript
// 用户权限检查
function checkUserPermission(action: string): boolean {
  const userRole = getCurrentUserRole()
  return hasPermission(userRole, action)
}

// 店铺访问权限
function checkStoreAccess(storeId: string): boolean {
  const userStores = getUserStores()
  return userStores.includes(storeId)
}
```

## 数据规范

### API接口规范
```typescript
// 统一API响应格式
interface ApiResponse<T> {
  code: number
  data: T
  message: string
  timestamp: number
}

// 分页数据格式
interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasNext: boolean
}
```

### 本地存储规范
```typescript
// 存储键名规范
const STORAGE_KEYS = {
  USER_INFO: 'smart-user-info',
  STORE_INFO: 'smart-store-info',
  CACHE_PREFIX: 'smart-cache-',
  SETTINGS: 'smart-settings'
}

// 存储操作封装
function setStorageData(key: string, data: any): void {
  try {
    wx.setStorageSync(key, JSON.stringify(data))
  } catch (error) {
    console.error('smart-storage-error, @utils/storage #setStorageData:', error)
  }
}
```

### 缓存策略规范
- API响应缓存时间设置
- 静态资源缓存策略
- 用户数据本地缓存
- 缓存失效和更新机制

## 支付业务规范

### 支付流程
1. 订单创建和验证
2. 支付方式选择
3. 支付参数准备
4. 调用支付接口
5. 支付结果处理
6. 订单状态更新

### 支付安全
```typescript
// 支付参数验证
function validatePaymentParams(params: PaymentParams): boolean {
  return (
    params.amount > 0 &&
    params.orderId &&
    params.merchantId &&
    params.timestamp
  )
}

// 支付结果验证
function verifyPaymentResult(result: PaymentResult): boolean {
  // 验证支付回调签名
  return verifySignature(result.signature, result.data)
}
```

## 日志记录规范

### 业务日志格式
```typescript
// 业务操作日志
function logBusinessOperation(
  operation: string,
  params: any,
  result: 'success' | 'error',
  error?: Error
) {
  const logData = {
    operation,
    params,
    result,
    error: error?.message,
    timestamp: Date.now(),
    userId: getCurrentUserId(),
    storeId: getCurrentStoreId()
  }
  
  console.log(`smart-business-${result}, @${operation}:`, logData)
}
```

### 性能监控日志
```typescript
// 页面性能日志
function logPagePerformance(pageName: string, loadTime: number) {
  console.log(`smart-performance-page, @${pageName} #loadTime: ${loadTime}ms`)
}

// API性能日志
function logApiPerformance(apiName: string, duration: number, success: boolean) {
  const status = success ? 'success' : 'error'
  console.log(`smart-performance-api-${status}, @${apiName} #duration: ${duration}ms`)
}
```

## 错误处理规范

### 业务异常分类
- **用户操作异常**: 输入验证失败、权限不足等
- **网络异常**: 请求超时、网络连接失败等
- **服务异常**: 服务器错误、数据异常等
- **系统异常**: 程序崩溃、内存不足等

### 异常处理策略
```typescript
// 统一异常处理
function handleBusinessError(error: BusinessError, context: string): void {
  // 记录错误日志
  console.error(`smart-business-error, @${context} #${error.code}: ${error.message}`)
  
  // 用户友好提示
  showUserFriendlyError(error)
  
  // 错误上报
  reportError(error, context)
}
```

## 国际化和本地化

### 文本规范
- 使用统一的文案管理系统
- 支持多语言切换
- 文案长度适配不同屏幕
- 敏感词过滤和合规检查

### 地域适配
- 时区处理
- 货币格式化
- 日期时间格式
- 本地法规遵循

## 数据合规

### 隐私保护
- 用户数据最小化收集
- 敏感信息加密存储
- 数据传输加密
- 用户授权管理

### 数据安全
```typescript
// 敏感数据脱敏
function maskSensitiveData(data: string, type: 'phone' | 'idcard' | 'bankcard'): string {
  switch (type) {
    case 'phone':
      return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    case 'idcard':
      return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    default:
      return data
  }
}
```