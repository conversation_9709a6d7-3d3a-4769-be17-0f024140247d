# 安全规范指南

## 数据安全

### 敏感信息保护
```typescript
// 禁止在代码中硬编码敏感信息
// ❌ 错误示例
const API_KEY = 'sk-abc123...'
const DATABASE_PASSWORD = 'mypassword123'

// ✅ 正确示例
const API_KEY = process.env.API_KEY
const DATABASE_PASSWORD = process.env.DATABASE_PASSWORD
```

### 数据传输安全
```typescript
// HTTPS 请求验证
function validateHttpsRequest(url: string): boolean {
  if (!url.startsWith('https://')) {
    console.error('smart-security-error, @request #validateHttpsRequest: 非HTTPS请求')
    return false
  }
  return true
}

// 请求头安全设置
const secureHeaders = {
  'Content-Type': 'application/json',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block'
}
```

### 本地存储安全
```typescript
// 敏感数据加密存储
import CryptoJS from 'crypto-js'

function encryptStorage(data: string, key: string): string {
  return CryptoJS.AES.encrypt(data, key).toString()
}

function decryptStorage(encryptedData: string, key: string): string {
  const bytes = CryptoJS.AES.decrypt(encryptedData, key)
  return bytes.toString(CryptoJS.enc.Utf8)
}

// 安全存储实现
function setSecureStorage(key: string, data: any): void {
  try {
    const encrypted = encryptStorage(JSON.stringify(data), getStorageKey())
    wx.setStorageSync(key, encrypted)
  } catch (error) {
    console.error('smart-security-error, @storage #setSecureStorage:', error)
  }
}
```

## 输入验证和过滤

### XSS 防护
```typescript
// HTML 转义
function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;")
}

// URL 验证
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url)
    return ['http:', 'https:'].includes(urlObj.protocol)
  } catch {
    return false
  }
}

// 输入内容过滤
function sanitizeInput(input: string): string {
  // 移除潜在的恶意脚本
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
}
```

### SQL 注入防护
```typescript
// 参数化查询（伪代码示例）
function safeQuery(sql: string, params: any[]): Promise<any> {
  // 使用参数化查询而不是字符串拼接
  return database.query(sql, params)
}

// ❌ 危险的字符串拼接
// const query = `SELECT * FROM users WHERE id = ${userId}`

// ✅ 安全的参数化查询
// const query = 'SELECT * FROM users WHERE id = ?'
// safeQuery(query, [userId])
```

## 认证和授权

### Token 安全管理
```typescript
// Token 存储和获取
const TOKEN_KEY = 'smart-auth-token'
const TOKEN_EXPIRY_KEY = 'smart-token-expiry'

function setAuthToken(token: string, expiryTime: number): void {
  const encryptedToken = encryptStorage(token, getTokenKey())
  wx.setStorageSync(TOKEN_KEY, encryptedToken)
  wx.setStorageSync(TOKEN_EXPIRY_KEY, expiryTime)
}

function getAuthToken(): string | null {
  try {
    const encryptedToken = wx.getStorageSync(TOKEN_KEY)
    const expiryTime = wx.getStorageSync(TOKEN_EXPIRY_KEY)
    
    if (!encryptedToken || Date.now() > expiryTime) {
      clearAuthToken()
      return null
    }
    
    return decryptStorage(encryptedToken, getTokenKey())
  } catch (error) {
    console.error('smart-security-error, @auth #getAuthToken:', error)
    return null
  }
}

function clearAuthToken(): void {
  wx.removeStorageSync(TOKEN_KEY)
  wx.removeStorageSync(TOKEN_EXPIRY_KEY)
}
```

### 权限验证
```typescript
// 用户权限检查
interface UserPermissions {
  canRead: boolean
  canWrite: boolean
  canDelete: boolean
  storeAccess: string[]
}

function checkPermission(action: string, resource?: string): boolean {
  const permissions = getUserPermissions()
  
  switch (action) {
    case 'read':
      return permissions.canRead
    case 'write':
      return permissions.canWrite
    case 'delete':
      return permissions.canDelete
    case 'store_access':
      return resource ? permissions.storeAccess.includes(resource) : false
    default:
      return false
  }
}

// 装饰器模式权限检查
function requirePermission(permission: string) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function(...args: any[]) {
      if (!checkPermission(permission)) {
        console.error(`smart-security-error, @${target.constructor.name} #${propertyKey}: 权限不足`)
        throw new Error('权限不足')
      }
      return originalMethod.apply(this, args)
    }
  }
}
```

## API 安全

### 请求签名验证
```typescript
// API 请求签名
import CryptoJS from 'crypto-js'

function generateSignature(params: object, secret: string): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&')
  
  return CryptoJS.HmacSHA256(sortedParams, secret).toString()
}

// 安全的 API 请求
function secureApiRequest(url: string, data: object): Promise<any> {
  const timestamp = Date.now()
  const nonce = generateNonce()
  const signature = generateSignature({ ...data, timestamp, nonce }, getApiSecret())
  
  const headers = {
    'X-Timestamp': timestamp.toString(),
    'X-Nonce': nonce,
    'X-Signature': signature,
    'Authorization': `Bearer ${getAuthToken()}`
  }
  
  return request(url, { data, headers })
}
```

### 请求频率限制
```typescript
// 简单的请求频率限制
class RateLimiter {
  private requests: Map<string, number[]> = new Map()
  
  isAllowed(identifier: string, limit: number, windowMs: number): boolean {
    const now = Date.now()
    const windowStart = now - windowMs
    
    const userRequests = this.requests.get(identifier) || []
    const validRequests = userRequests.filter(time => time > windowStart)
    
    if (validRequests.length >= limit) {
      console.warn(`smart-security-warning, @RateLimiter #isAllowed: 请求频率超限 ${identifier}`)
      return false
    }
    
    validRequests.push(now)
    this.requests.set(identifier, validRequests)
    return true
  }
}

const rateLimiter = new RateLimiter()

// 使用示例
function protectedApiCall(apiPath: string, data: any): Promise<any> {
  const userId = getCurrentUserId()
  
  if (!rateLimiter.isAllowed(userId, 10, 60000)) { // 10 requests per minute
    throw new Error('请求过于频繁，请稍后再试')
  }
  
  return secureApiRequest(apiPath, data)
}
```

## 错误处理安全

### 安全的错误信息
```typescript
// 错误信息安全处理
class SecurityError extends Error {
  public readonly code: string
  public readonly userMessage: string
  
  constructor(code: string, internalMessage: string, userMessage: string) {
    super(internalMessage)
    this.code = code
    this.userMessage = userMessage
  }
}

function handleSecurityError(error: SecurityError, context: string): void {
  // 记录详细错误信息（仅内部使用）
  console.error(`smart-security-error, @${context} #${error.code}: ${error.message}`)
  
  // 向用户显示安全的错误信息
  showUserError(error.userMessage)
  
  // 错误上报（脱敏后）
  reportSecurityError({
    code: error.code,
    context,
    timestamp: Date.now(),
    userId: getCurrentUserId()
  })
}

// ❌ 错误示例 - 暴露敏感信息
// throw new Error(`Database connection failed: ${dbPassword}`)

// ✅ 正确示例 - 安全的错误处理
// throw new SecurityError('DB_CONNECTION_FAILED', 'Database connection failed: connection timeout', '系统繁忙，请稍后重试')
```

## 代码安全审查清单

### 开发阶段检查
- [ ] 没有硬编码的密钥、密码或敏感信息
- [ ] 所有用户输入都经过验证和过滤
- [ ] 使用 HTTPS 进行数据传输
- [ ] 敏感数据在本地存储时已加密
- [ ] 实现了适当的认证和授权机制

### 部署前检查
- [ ] 移除了所有调试信息和测试代码
- [ ] 配置了适当的错误处理
- [ ] 设置了请求频率限制
- [ ] 验证了第三方依赖的安全性
- [ ] 实现了安全的日志记录

### 运行时监控
- [ ] 监控异常的API调用模式
- [ ] 检测可疑的用户行为
- [ ] 记录安全相关事件
- [ ] 定期安全扫描和漏洞检测

## 第三方依赖安全

### 依赖安全审查
```bash
# 定期检查依赖漏洞
npm audit
npm audit fix

# 使用安全的依赖版本
npm install package@latest
```

### 依赖使用规范
```typescript
// 仅导入需要的功能
import { debounce } from 'lodash' // ✅ 按需导入
// import * as _ from 'lodash' // ❌ 全量导入

// 验证第三方库的输入输出
function safeLodashGet(object: any, path: string, defaultValue?: any): any {
  if (!object || typeof path !== 'string') {
    return defaultValue
  }
  return _.get(object, path, defaultValue)
}
```

## 合规性要求

### 数据保护法规
- 遵循 GDPR、CCPA 等数据保护法规
- 实现用户数据删除权
- 提供数据导出功能
- 获得明确的用户同意

### 行业标准
- 遵循 OWASP 安全指南
- 实施 ISO 27001 信息安全管理
- 符合 PCI DSS（如涉及支付）
- 满足行业特定的安全要求