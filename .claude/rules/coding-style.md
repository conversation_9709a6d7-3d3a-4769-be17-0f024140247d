# 代码风格规范

## 命名规范

### 变量和函数命名
- **变量名**: 使用 camelCase，见名知意
  ```typescript
  const userInfo = {}
  const isLoading = false
  const handleSubmit = () => {}
  ```

- **常量名**: 使用 UPPER_SNAKE_CASE
  ```typescript
  const API_BASE_URL = 'https://api.example.com'
  const MAX_RETRY_COUNT = 3
  ```

- **组件名**: 使用 PascalCase
  ```typescript
  // 文件名: user-profile.ts
  export default UserProfile
  
  // 组件使用
  <UserProfile />
  ```

### 文件和目录命名
- **文件名**: 使用 kebab-case
  ```
  user-profile.ts
  order-history.wxml
  shopping-cart.less
  ```

- **目录名**: 使用 kebab-case
  ```
  components-v2/
  user-center/
  order-management/
  ```

## TypeScript 规范

### 类型定义
- 接口名使用 PascalCase，以 `I` 开头
  ```typescript
  interface IUserInfo {
    id: string
    name: string
    avatar?: string
  }
  ```

- 类型别名使用 PascalCase
  ```typescript
  type OrderStatus = 'pending' | 'confirmed' | 'delivered'
  type ApiResponse<T> = {
    code: number
    data: T
    message: string
  }
  ```

### 函数定义
- 明确函数参数和返回值类型
  ```typescript
  function calculateTotal(items: ICartItem[]): number {
    return items.reduce((sum, item) => sum + item.price * item.quantity, 0)
  }
  
  async function fetchUserInfo(userId: string): Promise<IUserInfo> {
    const response = await api.get(`/users/${userId}`)
    return response.data
  }
  ```

## 代码组织

### 导入顺序
1. Node.js 内置模块
2. 第三方库
3. 项目内部模块（按路径层级）

```typescript
// Node.js 内置模块
import fs from 'fs'

// 第三方库
import _ from '@wosai/emenu-mini-lodash'
import dayjs from '@wosai/emenu-mini-dayjs'

// 项目内部模块
import { getEnv } from '@utils/env'
import { BaseComponent } from '@BaseComponent'
import './index.less'
```

### 文件结构
```typescript
// 1. 导入声明
import statements...

// 2. 类型定义
interface/type definitions...

// 3. 常量定义
const CONSTANTS...

// 4. 主要逻辑
class/function definitions...

// 5. 默认导出
export default...
```

## 注释规范

### 函数注释
```typescript
/**
 * 计算购物车总价
 * @param items 购物车商品列表
 * @param discountRate 折扣率 (0-1)
 * @returns 计算后的总价
 */
function calculateCartTotal(items: ICartItem[], discountRate: number = 0): number {
  // 实现逻辑...
}
```

### 复杂逻辑注释
```typescript
// 处理微信和支付宝平台差异
if (isWeixin()) {
  // 微信平台特有逻辑
  wx.navigateTo({ url: '/pages/detail/index' })
} else if (isAlipay()) {
  // 支付宝平台特有逻辑  
  my.navigateTo({ url: '/pages/detail/index' })
}
```

## 错误处理

### 异步操作
```typescript
async function fetchData(): Promise<IApiResponse> {
  try {
    const response = await api.get('/data')
    return response.data
  } catch (error) {
    console.error('smart-api-error, @utils/api #fetchData:', error)
    throw new Error('数据获取失败')
  }
}
```

### 边界条件
```typescript
function processItems(items: IItem[]): IProcessedItem[] {
  if (!items || items.length === 0) {
    console.warn('smart-process-warning, @utils/processor #processItems: 空数据')
    return []
  }
  
  return items.map(item => {
    if (!item.id) {
      console.error('smart-data-error, @utils/processor #processItems: 缺少必要字段 id')
      return null
    }
    return processItem(item)
  }).filter(Boolean)
}
```

## 代码简洁性

### 避免深层嵌套
```typescript
// ❌ 不好的写法
function processOrder(order) {
  if (order) {
    if (order.items) {
      if (order.items.length > 0) {
        return order.items.map(item => processItem(item))
      }
    }
  }
  return []
}

// ✅ 好的写法
function processOrder(order: IOrder): IProcessedItem[] {
  if (!order?.items?.length) {
    return []
  }
  
  return order.items.map(item => processItem(item))
}
```

### 使用有意义的变量名
```typescript
// ❌ 不好的写法
const d = new Date()
const u = users.filter(x => x.age > 18)

// ✅ 好的写法
const currentDate = new Date()
const adultUsers = users.filter(user => user.age > 18)
```

## 性能考虑

### 避免不必要的重新计算
```typescript
// ❌ 在组件中避免
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// ✅ 预计算或缓存
const cachedResults = new Map()

function getProcessedData(input: string): ProcessedData {
  if (!cachedResults.has(input)) {
    cachedResults.set(input, heavyCalculation(input))
  }
  return cachedResults.get(input)
}
```

### 合理使用异步操作
```typescript
// ✅ 并行处理
const [userInfo, orderHistory, preferences] = await Promise.all([
  fetchUserInfo(userId),
  fetchOrderHistory(userId),
  fetchUserPreferences(userId)
])

// ❌ 串行处理（除非有依赖关系）
const userInfo = await fetchUserInfo(userId)
const orderHistory = await fetchOrderHistory(userId)
const preferences = await fetchUserPreferences(userId)
```