# 性能优化指南

## 代码性能优化

### 算法复杂度优化
```typescript
// ❌ 低效的查找方式 O(n²)
function findDuplicatesInefficient(arr: number[]): number[] {
  const duplicates: number[] = []
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (arr[i] === arr[j] && !duplicates.includes(arr[i])) {
        duplicates.push(arr[i])
      }
    }
  }
  return duplicates
}

// ✅ 高效的查找方式 O(n)
function findDuplicatesEfficient(arr: number[]): number[] {
  const seen = new Set<number>()
  const duplicates = new Set<number>()
  
  for (const item of arr) {
    if (seen.has(item)) {
      duplicates.add(item)
    } else {
      seen.add(item)
    }
  }
  
  return Array.from(duplicates)
}
```

### 函数优化
```typescript
// 防抖函数优化
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): T {
  let timeout: NodeJS.Timeout
  
  return ((...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }) as T
}

// 节流函数优化
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): T {
  let inThrottle: boolean
  
  return ((...args: any[]) => {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }) as T
}

// 使用示例
const optimizedSearch = debounce(searchFunction, 300)
const optimizedScroll = throttle(scrollHandler, 100)
```

### 内存管理
```typescript
// 避免内存泄漏
class ComponentManager {
  private listeners: Array<() => void> = []
  
  addListener(listener: () => void): void {
    this.listeners.push(listener)
  }
  
  // 确保清理事件监听器
  destroy(): void {
    this.listeners.forEach(cleanup => cleanup())
    this.listeners = []
  }
}

// 使用 WeakMap 避免强引用
const elementDataMap = new WeakMap<Element, any>()

function setElementData(element: Element, data: any): void {
  elementDataMap.set(element, data)
}

function getElementData(element: Element): any {
  return elementDataMap.get(element)
}
```

## 网络性能优化

### API 请求优化
```typescript
// 请求缓存机制
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  get(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }
  
  set(key: string, data: any, ttl: number = 300000): void { // 5分钟默认TTL
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
}

const apiCache = new ApiCache()

// 带缓存的API请求
async function cachedApiRequest(url: string, options: any = {}): Promise<any> {
  const cacheKey = `${url}-${JSON.stringify(options)}`
  
  // 尝试从缓存获取
  const cached = apiCache.get(cacheKey)
  if (cached) {
    console.log(`smart-performance-cache-hit, @api #${url}`)
    return cached
  }
  
  // 发起请求
  const startTime = Date.now()
  try {
    const response = await request(url, options)
    const duration = Date.now() - startTime
    
    console.log(`smart-performance-api-success, @${url} #duration: ${duration}ms`)
    
    // 缓存响应
    apiCache.set(cacheKey, response, 300000) // 5分钟缓存
    return response
  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`smart-performance-api-error, @${url} #duration: ${duration}ms`, error)
    throw error
  }
}
```

### 请求合并和批处理
```typescript
// API 请求批处理
class RequestBatcher {
  private batchQueue: Array<{ 
    id: string
    resolve: (value: any) => void
    reject: (error: any) => void 
  }> = []
  private batchTimer: NodeJS.Timeout | null = null
  
  addToBatch(id: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.batchQueue.push({ id, resolve, reject })
      
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => this.executeBatch(), 100)
      }
    })
  }
  
  private async executeBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return
    
    const batch = [...this.batchQueue]
    this.batchQueue = []
    this.batchTimer = null
    
    try {
      const ids = batch.map(item => item.id)
      const results = await this.batchRequest(ids)
      
      batch.forEach((item, index) => {
        item.resolve(results[index])
      })
    } catch (error) {
      batch.forEach(item => item.reject(error))
    }
  }
  
  private async batchRequest(ids: string[]): Promise<any[]> {
    const response = await request('/api/batch', {
      method: 'POST',
      data: { ids }
    })
    return response.data
  }
}

const batcher = new RequestBatcher()

// 使用批处理
async function getItemData(id: string): Promise<any> {
  return batcher.addToBatch(id)
}
```

## 小程序性能优化

### 页面加载优化
```typescript
// 页面预加载
Page({
  onLoad() {
    const startTime = Date.now()
    
    // 预加载关键数据
    this.preloadData()
    
    // 记录加载时间
    this.onReady = () => {
      const loadTime = Date.now() - startTime
      console.log(`smart-performance-page, @${this.route} #loadTime: ${loadTime}ms`)
    }
  },
  
  async preloadData() {
    try {
      // 并行加载多个数据源
      const [userInfo, storeInfo, menuData] = await Promise.all([
        this.loadUserInfo(),
        this.loadStoreInfo(),
        this.loadMenuData()
      ])
      
      this.setData({
        userInfo,
        storeInfo,
        menuData,
        loading: false
      })
    } catch (error) {
      console.error('smart-performance-error, @page #preloadData:', error)
      this.handleLoadError(error)
    }
  }
})
```

### 数据更新优化
```typescript
// 使用 setData 优化
Page({
  // ❌ 频繁的 setData 调用
  updateDataInefficient() {
    this.setData({ item1: 'value1' })
    this.setData({ item2: 'value2' })
    this.setData({ item3: 'value3' })
  },
  
  // ✅ 批量更新
  updateDataEfficient() {
    this.setData({
      item1: 'value1',
      item2: 'value2',
      item3: 'value3'
    })
  },
  
  // ✅ 使用路径更新避免大对象传递
  updateListItem(index: number, newData: any) {
    this.setData({
      [`list[${index}]`]: newData
    })
  }
})
```

### 图片和资源优化
```typescript
// 图片懒加载
Component({
  properties: {
    src: String,
    lazy: {
      type: Boolean,
      value: true
    }
  },
  
  data: {
    loaded: false,
    actualSrc: ''
  },
  
  observers: {
    'src': function(newSrc) {
      if (!this.data.lazy) {
        this.setData({ actualSrc: newSrc, loaded: true })
        return
      }
      
      // 使用 Intersection Observer 实现懒加载
      this.observeIntersection()
    }
  },
  
  observeIntersection() {
    const observer = this.createIntersectionObserver({
      rootMargin: '100px'
    })
    
    observer.relativeToViewport().observe('.lazy-image', (res) => {
      if (res.intersectionRatio > 0) {
        this.setData({
          actualSrc: this.data.src,
          loaded: true
        })
        observer.disconnect()
      }
    })
  }
})
```

## 渲染性能优化

### 列表渲染优化
```typescript
// 虚拟滚动实现
Component({
  properties: {
    items: Array,
    itemHeight: {
      type: Number,
      value: 50
    }
  },
  
  data: {
    visibleItems: [],
    scrollTop: 0,
    containerHeight: 0
  },
  
  ready() {
    this.calculateVisibleItems()
  },
  
  onScroll(e: any) {
    const scrollTop = e.detail.scrollTop
    this.setData({ scrollTop })
    this.calculateVisibleItems()
  },
  
  calculateVisibleItems() {
    const { scrollTop, containerHeight } = this.data
    const { items, itemHeight } = this.properties
    
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )
    
    const visibleItems = items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index,
      top: (startIndex + index) * itemHeight
    }))
    
    this.setData({ visibleItems })
  }
})
```

### 计算属性优化
```typescript
// 使用计算属性避免重复计算
Component({
  data: {
    items: [],
    filter: '',
    _filteredItemsCache: null,
    _lastFilter: ''
  },
  
  observers: {
    'items, filter': function(items, filter) {
      // 仅在数据变化时重新计算
      if (filter !== this.data._lastFilter) {
        this.calculateFilteredItems(items, filter)
      }
    }
  },
  
  calculateFilteredItems(items: any[], filter: string) {
    const startTime = Date.now()
    
    const filteredItems = filter
      ? items.filter(item => item.name.includes(filter))
      : items
    
    const duration = Date.now() - startTime
    console.log(`smart-performance-filter, @component #duration: ${duration}ms, count: ${filteredItems.length}`)
    
    this.setData({
      _filteredItemsCache: filteredItems,
      _lastFilter: filter
    })
  }
})
```

## 构建性能优化

### 代码分割
```typescript
// 动态导入减少初始包大小
async function loadFeature(featureName: string): Promise<any> {
  const startTime = Date.now()
  
  try {
    let module
    switch (featureName) {
      case 'analytics':
        module = await import('./features/analytics')
        break
      case 'charts':
        module = await import('./features/charts')
        break
      default:
        throw new Error(`Unknown feature: ${featureName}`)
    }
    
    const loadTime = Date.now() - startTime
    console.log(`smart-performance-dynamic-import, @${featureName} #loadTime: ${loadTime}ms`)
    
    return module
  } catch (error) {
    console.error(`smart-performance-error, @dynamic-import #${featureName}:`, error)
    throw error
  }
}
```

### 资源压缩和优化
```javascript
// webpack 配置优化示例
module.exports = {
  optimization: {
    // 代码分割
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          name: 'vendors',
          test: /[\\/]node_modules[\\/]/,
          priority: 10,
          chunks: 'initial'
        },
        common: {
          name: 'common',
          minChunks: 2,
          priority: 5,
          reuseExistingChunk: true
        }
      }
    },
    
    // 压缩配置
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true, // 移除 console
            drop_debugger: true
          }
        }
      })
    ]
  },
  
  // 外部依赖
  externals: {
    'lodash': '_'
  }
}
```

## 性能监控

### 性能指标收集
```typescript
// 性能监控工具
class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map()
  
  // 记录时间指标
  recordTiming(name: string, duration: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    this.metrics.get(name)!.push(duration)
  }
  
  // 获取统计信息
  getStats(name: string): { avg: number; min: number; max: number; count: number } | null {
    const timings = this.metrics.get(name)
    if (!timings || timings.length === 0) return null
    
    return {
      avg: timings.reduce((a, b) => a + b) / timings.length,
      min: Math.min(...timings),
      max: Math.max(...timings),
      count: timings.length
    }
  }
  
  // 清理过期数据
  cleanup(): void {
    const maxEntries = 1000
    this.metrics.forEach((timings, name) => {
      if (timings.length > maxEntries) {
        this.metrics.set(name, timings.slice(-maxEntries))
      }
    })
  }
}

const performanceMonitor = new PerformanceMonitor()

// 性能装饰器
function measurePerformance(name: string) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args: any[]) {
      const startTime = Date.now()
      try {
        const result = await originalMethod.apply(this, args)
        const duration = Date.now() - startTime
        performanceMonitor.recordTiming(`${name}.${propertyKey}`, duration)
        console.log(`smart-performance-method, @${name} #${propertyKey}: ${duration}ms`)
        return result
      } catch (error) {
        const duration = Date.now() - startTime
        console.error(`smart-performance-error, @${name} #${propertyKey}: ${duration}ms`, error)
        throw error
      }
    }
  }
}
```

### 内存使用监控
```typescript
// 内存监控
function monitorMemoryUsage(): void {
  const memoryInfo = wx.getSystemInfoSync()
  
  // 定期检查内存使用
  setInterval(() => {
    const currentMemory = getCurrentMemoryUsage() // 平台特定实现
    
    if (currentMemory > 100 * 1024 * 1024) { // 100MB 阈值
      console.warn('smart-performance-warning, @memory #usage: 内存使用过高', {
        current: currentMemory,
        device: memoryInfo
      })
      
      // 触发内存清理
      triggerMemoryCleanup()
    }
  }, 30000) // 每30秒检查一次
}

function triggerMemoryCleanup(): void {
  // 清理缓存
  apiCache.clear()
  
  // 通知组件清理
  getApp().globalData.needsCleanup = true
  
  console.log('smart-performance-cleanup, @memory #triggered')
}
```