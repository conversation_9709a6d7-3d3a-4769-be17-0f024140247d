# 项目特定规范

## 项目业务场景

### 目标群体
- **商家端**: 餐饮店铺、零售商户、校园商家
- **消费者端**: 点餐用户、外卖用户、校园用户

### 三种核心服务模式

#### 1. 堂食服务 (subscribe_order)
**轻餐模式**:
- 适用场景: 快餐、咖啡厅、茶饮店、轻食店
- 核心特征: 扫桌台码点餐、店铺信息展示、远程购物车、本地购物车、就餐方式选择、打包费计算

**正餐(围餐)模式**:
- 适用场景: 烧烤、中餐厅、多人围坐用餐
- 核心特征: 扫桌台码点餐、店铺信息展示、远程购物车、长轮询实时同步、不支持打包费

#### 2. 外卖服务 (take_out_order)
- 适用场景: 配送到家、办公室订餐、校园外卖
- 核心功能: 收货地址管理、送达时间选择、配送费计算、打包费、实时配送费计算、配送范围检测

#### 3. 自取服务 (pre_order)
- 适用场景: 到店取餐、避免排队、预约取餐
- 核心功能: 自取时间选择、预留电话、打包费

## 环境要求

### 开发环境
- **Node.js版本**: v20 (使用 `nvm use 20` 确保正确版本)
- **构建工具**: MorJS框架，配置见 `mor.config.ts`
- **平台支持**: 微信和支付宝小程序跨平台编译

### 开发命令
```bash
# 环境准备
nvm use 20
npm install

# 平台开发
npm run dev:wechat    # 微信小程序开发
npm run dev:alipay    # 支付宝小程序开发

# 构建输出
dist/wechat          # 微信小程序构建目录
dist/alipay          # 支付宝小程序构建目录
```

## 项目配置

### 配置文件清单
- `project.config.json` - 微信小程序项目配置
- `tsconfig.json` - TypeScript配置
- `.eslintrc.js` - ESLint配置
- `jest.config.js` - Jest测试配置

### 测试执行
```bash
npm test              # 执行所有测试
npm run lint          # ESLint代码检查
```

## 模块结构规范

### 模块化业务包
通过 `package_name` 环境变量控制的不同业务模块：

#### home模块 - 点单系统
**包含页面**:
- `pages/router/index` - 路由入口
- `pages/home/<USER>
- `pages/search/index` - 搜索页面
- `pages/stores/index` - 店铺列表
- `pages/submit/index` - 订单提交
- `pages/address/index` - 地址管理
- `pages/remark/index` - 备注页面

**核心功能**:
- 商品浏览: 分类展示、搜索功能
- 购物车管理: 加减购、清空购物车
- 规格选择: 商品规格、口味定制

#### order模块 - 订单管理
**包含页面**:
- `pages/group-meal-post-*` - 后付围餐相关页面
- `pages/group-meal-pre-*` - 先付围餐相关页面
- `pages/orders/index` - 订单列表
- `pages/order-detail/index` - 订单详情
- `pages/pay/*` - 支付相关页面
- `pages/refund-*` - 退款相关页面

**核心功能**:
- 订单管理: 订单列表、状态跟踪、历史订单
- 围餐服务: 先付围餐、后付围餐
- 支付系统: 支付页面、收银台支付
- 退款服务: 退款申请、详情、取消

#### store模块 - 商家信息
**包含页面**:
- `pages/store-home/index` - 店铺首页
- `pages/purchased-list/index` - 消费过的门店

**核心功能**:
- 商家展示: 基础信息、封面图、详细信息
- 服务类型: 堂食/外卖/自取选择
- 商家收藏: 关注收藏功能

#### center模块 - 用户中心
**包含页面**:
- `pages/orders/index` - 订单中心
- `pages/collection/index` - 收藏管理

**核心功能**:
- 订单统一管理入口
- 收藏的商家管理

### 目录结构规范

#### 组件目录结构
```
src/
├── components_v2/          # 业务组件
│   ├── [component-name]/
│   │   ├── index.wxml     # 组件模板
│   │   ├── index.ts       # 组件逻辑
│   │   ├── index.less     # 组件样式
│   │   ├── index.json5    # 组件配置
│   │   ├── index.wxs      # 视图层逻辑
│   │   ├── template/      # 模板片段
│   │   ├── styles/        # 样式文件
│   │   ├── components/    # 子组件
│   │   └── utils/         # 工具函数
├── pages/                 # 页面组件
│   ├── [page-name]/
│   │   ├── index.wxml
│   │   ├── index.less
│   │   ├── index.ts
│   │   ├── index.json5
│   │   ├── components/    # 页面私有组件
│   │   ├── template/      # 模板片段
│   │   ├── utils/         # 页面工具函数
│   │   ├── styles/        # 页面样式
│   │   └── __tests__/     # 单元测试
├── lib/ui/               # 基础UI组件
└── styles/               # 公共样式
```

#### 命名规范
- **文件命名**: kebab-case (如: goods-detail)
- **组件命名**: 前缀+功能 (如: smart-goods-detail)
- **样式命名**: BEM规范 (如: goods-item__image, goods-item--horizontal)

## 页面开发规范

### 文件组织要求
每个页面必须包含四个主要文件：
- `.wxml` - 视图模板
- `.less` - 样式文件  
- `.ts` - 逻辑文件
- `.json5` - 配置文件

### 页面容器规范
- 使用 `@components_v2/page-container` 作为最外层容器
- 生命周期遵循小程序组件生命周期：created、attached、ready、detached
- 复杂页面使用多阶段渲染：beforeRender、render、afterRender

### 行为组合
使用以下 Behavior 组合页面逻辑：
- `useThemeBehavior` - 主题相关
- `useI18nBehavior` - 国际化相关  
- `useRenderBehavior` - 渲染相关
- `usePerformanceBehavior` - 性能相关
- `useBusinessCheckBehavior` - 业务逻辑检查

### 性能优化
- 大型列表使用懒加载和虚拟列表
- 重复渲染组件使用纯组件优化
- 使用 WXS 处理视图层逻辑，减少逻辑层通信
- 复杂页面使用分阶段渲染策略

### 路由管理
使用 `@pages/router` 模块管理页面跳转：
- 页面路径使用常量管理，避免硬编码
- 页面参数通过 URL query 参数传递