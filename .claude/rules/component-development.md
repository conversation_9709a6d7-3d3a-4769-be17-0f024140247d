# 组件开发指南

## 业务组件分类

### 商品与定价组件
- `@goods` - 商品组件，商品列表和展示
- `@goods-detail` - 商品详情组件，展示商品详细信息  
- `@goods-skeleton` - 商品骨架屏组件，优化加载体验
- `@food-item` - 食品项组件，餐饮类商品展示
- `@price` - 价格组件，统一价格显示逻辑
- `@price-activity` - 价格活动组件，折扣促销信息
- `@discount-tag` - 折扣标签组件，优惠信息展示
- `@discount-list` - 折扣列表组件，多项优惠内容
- `@tag-sales-rank` - 销量排名标签组件，热销信息
- `@invalid-goods-dialog` - 无效商品弹窗组件

### 店铺相关组件
- `@store-basic` - 店铺基础信息组件（名称、评分、距离）
- `@store-cover` - 店铺封面组件，展示店铺主图
- `@store-collection` - 店铺收藏组件，提供收藏功能
- `@store-info` - 店铺详细信息组件
- `@store-item` - 店铺列表项组件，店铺列表展示
- `@store-service-types` - 店铺服务类型组件（配送/自提等）
- `@banner-nav` - 横幅导航组件，店铺活动或导航信息
- `@brand-expose` - 品牌曝光组件，展示品牌形象

### 订单与支付组件
- `@order-card` - 订单卡片组件，展示订单信息
- `@order-map` - 订单地图组件，配送位置展示
- `@payment-summary` - 支付摘要组件，费用明细
- `@cashier` - 收银台组件，处理支付流程
- `@submit-bar` - 提交栏组件，下单确认
- `@refund-panel` - 退款面板组件，退款流程
- `@hbfq` - 花呗分期组件，分期付款选项

### 团购与拼餐组件
- `@group-meal-batch-list` - 团餐批次列表组件
- `@group-meal-btn-group` - 团餐按钮组组件
- `@group-meal-edit-people` - 团餐人数编辑组件
- `@counter` - 计数器组件，数量选择
- `@dialog-counter` - 对话框计数器组件
- `@dialog-user-counter` - 用户数量对话框组件

### 布局与交互组件
- `@page-container` - 页面容器组件，统一页面结构
- `@floating-bubble` - 悬浮气泡组件，悬浮交互入口
- `@expand` - 展开组件，内容折叠与展开
- `@keyboard` - 键盘组件，自定义键盘输入
- `@fast-panel` - 快速面板组件，快捷操作入口
- `@call` - 电话组件，一键拨号功能
- `@image-swiper` - 图片轮播组件，多图切换
- `@preview` - 预览组件，图片或商品预览
- `@safe-bottom` - 安全底部组件，适配设备安全区域

### 提示与反馈组件
- `@alert` - 警告组件，展示警告信息
- `@badge` - 徽章组件，数量或状态标记
- `@bulletin` - 公告组件，系统或商家公告
- `@notice-bar` - 通知栏组件，滚动通知
- `@dialog-material` - 材料对话框组件，商品材料信息
- `@full-loading` - 全屏加载组件，优化加载体验
- `@loading-flicker` - 加载闪烁效果组件，加载动效

### 营销与广告组件
- `@ads` - 广告组件，展示广告内容
- `@adsense` - 广告感知组件，智能推送广告
- `@adsense-list` - 广告列表组件，多个广告展示
- `@card` - 卡片组件，优惠卡或会员卡
- `@recommend-material` - 推荐材料组件，个性化推荐
- `@header-icons` - 头部图标组件，功能入口

### 全局提供者组件
- `@i18n-provider` - 国际化提供者组件，多语言处理
- `@main-provider` - 主要数据提供者组件，全局数据
- `@main-provider-theme` - 主题提供者组件，主题样式
- `@generics-default` - 通用默认组件，通用组件模板

## 4阶段开发流程

### 阶段1: 组件分析
**设计稿解析**:
1. 解析设计稿截图，明确以下要素：
   - 核心功能目标（商品展示、价格计算、用户交互）
   - 主要交互行为（点击、滑动、悬停效果）
   - 响应式布局要求（不同屏幕尺寸适配）
   - 跨平台差异（微信/支付宝/其他小程序差异）

2. HTML/CSS结构分析：
   - 识别基础UI组件依赖（按钮、图标、弹窗、标签）
   - 检查 `src/lib/ui` 目录下组件复用可能性
   - 评估现有业务组件复用可能性
   - 确认样式类名遵循项目通用标准
   - 分析动态变化需求（展开/收起、选中状态切换）

### 阶段2: 组件设计
**技术文档查阅**:
- 使用文档工具查看所需UI组件和业务组件开发文档
- 分析相似业务组件实现规范（参考 @store-item）
- 调研平台兼容性考虑（微信、支付宝API差异）
- 了解patch系统处理跨平台差异方式

### 阶段3: 组件实现
**目录结构创建**:
```
src/components_v2/[component-name]/
├── index.wxml          # 组件模板
├── index.ts            # 组件逻辑
├── index.less          # 组件样式
├── index.json5         # 组件配置
├── index.wxs           # 视图层逻辑脚本
├── template/           # 可重用模板片段
│   └── _*.wxml         # 下划线前缀命名
├── styles/             # 样式相关文件
├── components/         # 子组件
└── utils/              # 组件工具函数
```

**代码实现规范**:
- 使用 `BaseComponent` 代替原生 `Component`
- 组合/编排UI基础组件和可复用业务组件
- 使用 `.ts` 文件编写逻辑，可忽略TS类型错误
- 使用slot和多槽位支持内容注入
- 使用template组织和复用WXML片段
- 使用WXS增强视图层处理能力
- 处理平台兼容性问题（条件编译）

### 阶段4: 组件完善
**代码评估与改进**:
- 分析现有组件问题和改进点：代码复用性、性能优化、可维护性
- 制定完善策略，保持向后兼容
- 参考类似业务组件保持代码风格一致性
- 调整样式和WXML模板文件，不修改逻辑文件

## 文件组织规范

### 命名规范
- **组件命名**: kebab-case格式（如：goods-detail）
- **文件命名**: index.* 作为主文件
- **子目录**: 复杂组件使用目录结构组织

### 基础文件结构
```typescript
// index.json5 - 组件配置
{
  "component": true,
  "usingComponents": {
    "smart-ui-button": "@wosai/smart-mp-ui/button/index",
    "smart-ui-icon": "@wosai/smart-mp-ui/icon/index",
    "smart-counter": "@components_v2/counter/index"
  }
}

// index.ts - 组件逻辑
import BaseComponent from '@BaseComponent'
import _ from '@wosai/emenu-mini-lodash'

BaseComponent({
  options: {
    multipleSlots: true,
    lifetimes: true
  },
  externalClasses: ['custom-class'],
  properties: {
    item: { type: Object, value: {} },
    customStyle: { type: String, value: '' }
  },
  data: {
    loading: false,
    expanded: false
  },
  lifetimes: {
    ready() {
      this.init()
    }
  },
  methods: {
    init() {
      // 初始化逻辑
    }
  }
})

// index.less - 组件样式
@import (css) '@styles/common';
@import (css) '@styles/flex';

.smart-component-name {
  &.container { /* 容器样式 */ }
  
  .body {
    .info { /* 信息区域 */ }
    .actions { /* 操作区域 */ }
  }
}

// index.wxml - 组件模板
<view class="smart-component-name container">
  <view class="body">
    <view class="info">
      <!-- 内容区域 -->
    </view>
    <view class="actions">
      <smart-ui-button bind:tap="handleAction">
        操作
      </smart-ui-button>
    </view>
  </view>
</view>
```

## 样式规范

### LESS预处理器使用
```less
// 引入公共样式
@import (css) '@styles/common';
@import (css) '@styles/flex';

// 层级嵌套规范
.smart-component {
  &.container { /* 容器样式 */ }
  
  .body {
    .info {
      .info-header { /* 信息头部 */ }
      .options-container { /* 选项容器 */ }
    }
  }
  
  .footer-container { /* 底部容器 */ }
}
```

### 命名规范
- **BEM命名**: 使用BEM规范增强语义化
- **主题变量**: 支持CSS变量定制
- **样式隔离**: 使用apply-shared模式
- **外部样式**: 使用externalClasses支持自定义注入

### 基础UI组件复用
```json
{
  "usingComponents": {
    "smart-ui-action-sheet": "@wosai/smart-mp-ui/action-sheet/index",
    "smart-ui-icon": "@wosai/smart-mp-ui/icon/index",
    "smart-ui-button": "@wosai/smart-mp-ui/button/index",
    "smart-ui-tag": "@wosai/smart-mp-ui/tag/index",
    "smart-counter": "@components_v2/counter/index",
    "smart-price": "@components_v2/price/index",
    "smart-badge": "@components_v2/badge/index"
  }
}
```

## 平台兼容性处理

### 条件编译
```typescript
const isWeixin = () => typeof wx !== 'undefined' && !!wx.getSystemInfo

// 使用示例
observers: {
  store(newVal) {
    if (newVal) {
      this.setData({
        activityMessages: newVal[isWeixin() ? 'wechatActivityMessages' : 'alipayActivityMessages'] || []
      })
    }
  }
}
```

### Patch系统
- 使用项目中的patch机制处理平台差异
- 条件编译区分多平台差异（#ifdef wechat/#ifdef alipay）

## 注意事项

### 开发工具使用
1. **文档查阅**: 必须使用文档工具查看组件开发文档
2. **不确定性处理**: 主动询问用户如何继续，不猜测决定
3. **效率优化**: 同时调用多个相关工具，而非顺序执行
4. **示例组件**: 完成后提供相对路径的示例组件用于预览

### 性能考虑
- 控制选择器嵌套不超过4层
- 使用WXS处理复杂显示逻辑，减少逻辑层通信
- 复杂组件拆分为子组件提升复用性