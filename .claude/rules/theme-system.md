# 主题系统规范

## 主题配置系统概述

主题系统通过配置CSS变量值实现多种主题模板，采用从后端模板数据到前端样式的完整数据流转机制。

## 13种主题配置

### 配置1: 全局主题色 (ID: 7b3a5e9f2c1d)
**全局通用主题变量**

| 变量名 | 用途 | 示例值 |
|--------|------|--------|
| `--primary__color` | 主色值 | `#FF6A16` |
| `--primary-foreground__color` | 主色前景色 | `#FFFFFF` |
| `--primary-gradient__color` | 渐变色 | `linear-gradient(270deg, #FF6C17 0%, #FF9434 100%)` |
| `--primary-gradient-foreground__color` | 渐变前景色 | `#FFFFFF` |
| `--secondary__color` | 辅助色 | `#FF4D4D` |
| `--secondary-foreground__color` | 辅助色前景色 | `#FFFFFF` |
| `--primary-selected__color` | 选中背景色 | `rgba(255, 106, 22, 0.1)` |
| `--discount-tag__color` | 折扣信息标签颜色 | `#ff4d4d` |

### 配置2: 点单首页背景图 (ID: 1a2b3c4d5e6f)
- **变量**: `--main__bg-img`
- **用途**: 点单页背景图
- **示例**: `url(/images/home-bg.png)`

### 配置3: 分类导航图标 (ID: 9g8h7i6j5k4l)
**分类导航相关图标**
- `--hot-sale-icon__bg-img` - 热销图标
- `--discount-icon__bg-img` - 优惠图标  
- `--recommend-icon__bg-img` - 推荐图标
- `--hot-sale-label__bg-img` - 热销分类标签
- `--recommend-goods-label__bg-img` - 商品推荐分类标签
- `--ordered-icon__bg-img` - 点过的图标

### 配置4: 商品信息样式 (ID: 3m2n1o0p9q8r)
**商品信息组件样式**

#### 商品标签样式
支持3种样式模式：

**样式1**:
- `--tag__bg-color`: 标签底色
- `--tag__color`: 文字颜色

**样式2**:
- `--tag__bg-color`: 标签底色
- `--tag__color`: 文字颜色
- `--tag__border-color`: 描边颜色
- `--tag__bg-img`: 图标

**样式3**:
- `--tag-left__bg-color`: 左侧标签底色
- `--tag-left__color`: 左侧文字颜色
- `--tag-right__bg-color`: 右侧标签底色
- `--tag-right__color`: 右侧文字颜色

### 配置5: 商品推荐背景 (ID: 7s6t5u4v3w2x)
- **变量**: `--recommend-goods-icon__bg-img`
- **用途**: 商品推荐背景图
- **示例**: `url(/images/recommend-bg.png)`

### 配置6: 结算栏样式 (ID: 1y2z3a4b5c6d)
**结算栏组件样式**
- `--cart__bg-img` - 购物车图标
- `--price__color` - 结算栏数字颜色
- `--bg-color` - 结算栏背景颜色
- `--backdrop-filter` - 结算栏背景模糊
- `--border-color` - 结算栏边框颜色

### 配置7: 提交订单页背景 (ID: 3k2l1m0n9o8p)
- **变量**: `--main__bg-img`
- **用途**: 提交订单页背景图
- **示例**: `url(/images/submit-bg.png)`

### 配置8: 就餐方式图标 (ID: 7q6r5s4t3u2v)
**提交订单页就餐方式图标**
- `--pack-icon__bg-img` - 打包图标
- `--dinein-icon__bg-img` - 堂食图标

### 配置9: 推荐加料图标 (ID: 1w2x3y4z5a6b)
- **变量**: `--recommend-material-icon__bg-img`
- **用途**: 商品详情页推荐加料图标
- **示例**: `url(/icons/recommend-material.png)`

### 配置10: 就餐方式弹窗 (ID: 7l7kmqaopfpl)
**点单首页就餐方式选择弹窗**
- `--subscriber-icon__bg-img` - 扫一扫店内下单图标
- `--takeout-icon__bg-img` - 外卖配送图标
- `--pre-icon__bg-img` - 到店自取图标

### 配置11: 门店首页背景 (ID: 9d5sttb89yix)
**门店首页背景样式**
- `--bg-img` - 门店首页背景图
- `--bg-color` - 背景色

### 配置12: 商铺信息样式 (ID: d74yeseqrz7w)
**门店首页商铺信息样式**
- `--bg-color` - 组件背景色
- `--content__color` - 通用文字颜色
- `--icon__color` - 电话与地址图标颜色
- `--title__color` - 商铺名称颜色

### 配置13: 服务列表样式 (ID: q3ny8z6yr2ub)
**门店首页服务列表样式**
支持2种样式模式：

**样式1**:
- 服务图标: `--dinein-icon__bg-img`, `--takeout-icon__bg-img`, `--pre-icon__bg-img`, `--pay-icon__bg-img`
- 文字颜色: `--item-title__color`, `--item-content__color`
- 背景色: `--item-primary__bg-color`, `--item-secondary__bg-color`
- 箭头颜色: `--item-arrow-icon_color`

**样式2**:
- 服务图标: 同上
- 文案颜色: `--item-content__color`
- 箭头颜色: `--item-arrow-icon_color`

## 技术实现原理

### 数据流程
```
后端模板数据 → 模板数据处理 → CSS变量生成 → 页面样式应用
```

### 主题变量命名规则

#### 特殊组件处理 (7b3a5e9f2c1d - 自定义配色)
```javascript
// 直接使用原始变量名，作为全局主题变量
acc[`${key}`] = value
// 例如：--primary__color、--primary-gradient__color
```

#### 有配置组件处理
```javascript
// 格式：--变量名-配置值-组件ID
acc[`${key}-${config[configKey]}-${component_id}`] = value
// 例如：--tag__bg-color-style1-3m2n1o0p9q8r
```

#### 无配置组件处理
```javascript
// 格式：--变量名-组件ID
acc[`${key}-${component_id}`] = value
// 例如：--main__bg-img-1a2b3c4d5e6f
```

### 特殊处理规则
**商品信息组件背景图**:
```javascript
if (component_id === '3m2n1o0p9q8r' && key === '--bg-img') {
  acc[`${key}-${component_id}`] = value
}
```

## 图片资源优化处理

### 图片压缩策略
系统自动对图片资源进行压缩和格式转换：

#### 通用图片处理
```javascript
// 转换为 WebP 格式
url(原始URL?x-oss-process=image/format,webp)
```

#### 特定尺寸处理
```javascript
// 导航栏背景图：750x550
// 商家首页背景：750x560  
// 就餐方式图标：110x110
url(原始URL?x-oss-process=image/resize,w_750,h_550,limit_0/quality,Q_75/format,jpg/interlace,1)
```

### 图片处理映射表

| 组件类型 | 变量名 | 尺寸规格 | 格式 |
|---------|--------|----------|------|
| 导航栏背景 | `--main__bg-img` | 750×550 | JPG |
| 商家首页背景 | `--bg-img` (9d5sttb89yix) | 750×560 | JPG |
| 就餐方式图标 | `--pre-icon__bg-img` 等 | 110×110 | PNG |
| 其他图片 | 通用处理 | 原尺寸 | WebP |

## 数据结构规范

### 输入数据格式
```json
{
  "template": {
    "components": [
      {
        "component_id": "7b3a5e9f2c1d",
        "component_name": "自定义配色",
        "config": {},
        "style": {
          "--primary__color": "#FF6A16",
          "--primary-gradient__color": "linear-gradient(270deg, #FF6C17 0%, #FF9434 100%)"
        }
      },
      {
        "component_id": "1a2b3c4d5e6f",
        "component_name": "头部信息",
        "config": {},
        "style": {
          "--main__bg-img": "url(/images/home-bg.png)"
        }
      }
    ],
    "settings": {
      "navigationBarFrontColor": "#FFFFFF"
    }
  }
}
```

### 输出主题对象
```javascript
{
  style: {
    // 全局变量（来自自定义配色组件）
    "--primary__color": "#FF6A16",
    "--primary-gradient__color": "linear-gradient(270deg, #FF6C17 0%, #FF9434 100%)",
    
    // 组件特定变量
    "--main__bg-img-1a2b3c4d5e6f": "url(处理后的图片URL)",
    "--cart__bg-img-1y2z3a4b5c6d": "url(...)",
    
    // 导航栏颜色
    "--navigation-bar__color": "#FFFFFF"
  },
  config: {
    // 合并所有组件配置
  },
  settings: {
    // 导航栏设置等
    "navigationBarColor": "#000000"
  }
}
```

## 实现代码示例

### 主题数据获取与缓存
```javascript
const TEMPLATE_EFFECTIVE_TIME = 5 * 60 * 1000; // 5分钟有效期

// 获取主题配置
async function getThemeConfig() {
  try {
    // 检查缓存
    const cached = wx.getStorageSync('theme_template');
    const cachedTime = wx.getStorageSync('theme_template_time');
    
    if (cached && cachedTime && 
        Date.now() - cachedTime < TEMPLATE_EFFECTIVE_TIME) {
      return cached;
    }
    
    // 获取新数据
    const template = await sqbBridge.getTemplate();
    
    // 缓存数据
    wx.setStorageSync('theme_template', template);
    wx.setStorageSync('theme_template_time', Date.now());
    
    return template;
  } catch (error) {
    console.error('获取主题配置失败:', error);
    return getDefaultTheme();
  }
}
```

### CSS变量生成
```javascript
function generateCSSVars(template) {
  const excludeComponentIds = ['1phytnqnsuqm', 'nkjdg45l765g'];
  const style = {};
  
  template.components.forEach(comp => {
    if (excludeComponentIds.includes(comp.component_id)) return;
    
    Object.entries(comp.style || {}).forEach(([key, value]) => {
      let varName = key;
      
      // 特殊处理
      if (comp.component_id === '7b3a5e9f2c1d') {
        // 全局变量直接使用
        style[key] = value;
      } else if (comp.config && Object.keys(comp.config).length > 0) {
        // 有配置组件
        const configKey = Object.keys(comp.config)[0];
        const configValue = comp.config[configKey];
        varName = `${key}-${configValue}-${comp.component_id}`;
      } else {
        // 无配置组件
        varName = `${key}-${comp.component_id}`;
      }
      
      // 特殊处理商品背景图
      if (comp.component_id === '3m2n1o0p9q8r' && key === '--bg-img') {
        varName = `${key}-${comp.component_id}`;
      }
      
      style[varName] = processImageUrl(value);
    });
  });
  
  return style;
}
```

### 页面应用
```xml
<!-- 在页面根元素注入所有CSS变量 -->
<view class="smart-page" style="{{computed.toCSSVars(theme.style)}}">
  <!-- 页面内容 -->
</view>
```

```less
// 在LESS样式中使用
.page-home {
  // 引用全局主题变量
  --home-primary-color: var(--primary__color);
  
  // 引用组件特定变量
  --home-main-background-image: var(--main__bg-img-1a2b3c4d5e6f);
  
  .content {
    background-image: var(--home-main-background-image, none);
    background-color: var(--bg-color-9d5sttb89yix, #F5F5F5);
  }
}
```

## 使用注意事项

### 缓存策略
- 主题数据本地缓存有效期：5分钟
- 自动清理过期缓存
- 支持强制刷新主题配置

### 图片处理
- 自动压缩和格式转换
- 支持WebP格式优先
- 特定尺寸自动适配

### 配置管理
- 支持动态更新主题配置
- 保持向后兼容性
- 提供默认主题配置作为回退