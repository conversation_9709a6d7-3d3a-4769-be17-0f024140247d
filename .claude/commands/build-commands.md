# 构建命令参考

## 常用构建命令

### 开发环境
```bash
# 启动开发服务器（热重载）
npm run dev

# 启动开发服务器（指定环境）
npm run dev:test
npm run dev:staging
npm run dev:prod
```

### 生产构建
```bash
# 生产环境构建
npm run build

# 不同环境构建
npm run build:test
npm run build:staging  
npm run build:prod
```

### 代码检查
```bash
# ESLint 检查
npm run lint
npm run lint:fix

# TypeScript 类型检查
npm run typecheck

# Stylelint 样式检查
npm run stylelint
npm run stylelint:fix
```

### 测试命令
```bash
# 运行所有测试
npm test
npm run test:unit
npm run test:e2e

# 测试覆盖率
npm run test:coverage

# 监听模式测试
npm run test:watch
```

## MorJS 相关命令

### 小程序构建
```bash
# 微信小程序构建
mor compile --target wechat

# 支付宝小程序构建  
mor compile --target alipay

# 多端构建
mor compile --target wechat,alipay
```

### 开发调试
```bash
# 启动开发模式
mor dev --target wechat

# 开启热重载
mor dev --target wechat --hot

# 指定端口
mor dev --target wechat --port 3000
```

## 包管理命令

### 依赖管理
```bash
# 安装依赖
npm install

# 安装开发依赖
npm install --save-dev <package>

# 安装生产依赖
npm install --save <package>

# 更新依赖
npm update

# 审计安全漏洞
npm audit
npm audit fix
```

### 清理操作
```bash
# 清理 node_modules
rm -rf node_modules package-lock.json
npm install

# 清理构建缓存
npm run clean
rm -rf dist build .cache

# 清理开发缓存
rm -rf .mor .temp .webpack_cache
```

## 打包分析

### 包大小分析
```bash
# 分析包大小
npm run analyze

# 查看依赖关系
npm ls
npm ls --depth=0

# 查看包信息
npm info <package>
```

### 性能分析
```bash
# 构建性能分析
npm run build --verbose

# 启动时间分析
npm run dev --profile

# 内存使用分析
node --inspect npm run build
```

## 环境变量配置

### 开发环境
```bash
# 设置开发环境
export NODE_ENV=development
export API_BASE_URL=https://dev-api.example.com

# 使用 .env 文件
cp .env.example .env.local
```

### 生产环境
```bash
# 设置生产环境
export NODE_ENV=production
export API_BASE_URL=https://api.example.com

# CI/CD 环境变量
export CI=true
export BUILD_VERSION=$CI_COMMIT_SHA
```

## CI/CD 命令

### 持续集成
```bash
# 安装依赖（CI 环境）
npm ci

# 运行所有检查
npm run ci:check

# 生成构建报告
npm run build:report
```

### 部署命令
```bash
# 部署到测试环境
npm run deploy:test

# 部署到预发布环境
npm run deploy:staging

# 部署到生产环境
npm run deploy:prod
```

## 工具链命令

### 代码格式化
```bash
# Prettier 格式化
npm run format
npm run format:check

# 文件权限修复
chmod +x scripts/*.sh
```

### 文档生成
```bash
# 生成 API 文档
npm run docs:api

# 生成组件文档
npm run docs:components

# 启动文档服务器
npm run docs:serve
```

## 故障排查命令

### 诊断信息
```bash
# Node.js 版本信息
node --version
npm --version

# 系统信息
npm config list
npm config get registry

# 网络诊断
npm ping
npm doctor
```

### 重置环境
```bash
# 重置 npm 缓存
npm cache clean --force

# 重置 Git 状态
git clean -fd
git reset --hard HEAD

# 完全重置项目
rm -rf node_modules package-lock.json
npm install
```