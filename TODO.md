# 韦旭红
[] 网格模式必点商品背景样式
[x] 【微信久久折】【鸿蒙】【收钱吧餐饮】【轻餐-堂食】扫码问题 https://jira.wosai-inc.com/browse/SMART-22692?filter=-1
[x] [扫一扫后购物车失效](https://sqb.feishu.cn/wiki/OwByw4lHhi2xKskFdXecWtsrn6w?sheet=TCcvIp&rangeId=TCcvIp_TwkZAm4Csa&rangeVer=1)

[x] 支付宝

[] 主题预览， 使所有页面可预览

[] iOS 14

[x] 第36个问题，购物车里，如果商品明细很多的话，应该有折叠效果 [SMART-22559](https://jira.wosai-inc.com/browse/SMART-22559)

[x] 当加购数量超过999时，购物车、商品分类上面的红点的数量，显示位数没有旧版多  [SMART-22580](https://jira.wosai-inc.com/browse/SMART-22580)

[x] 不展示储值专享价 [SMART-22530](https://jira.wosai-inc.com/browse/SMART-22530)

[x] [背景这里怎么有一个图片？](https://sqb.feishu.cn/wiki/OwByw4lHhi2xKskFdXecWtsrn6w?sheet=7lcr4x&rangeId=7lcr4x_PnCUMBwjJs&rangeVer=1)

[x] [“点过“的商品名称字体是不是比下面列表的商品名称字段粗？肉眼看上去感觉不一样](https://sqb.feishu.cn/wiki/OwByw4lHhi2xKskFdXecWtsrn6w?sheet=7lcr4x&rangeId=7lcr4x_BKhPALfidO&rangeVer=1)

[] [热卖图标](https://sqb.feishu.cn/wiki/OwByw4lHhi2xKskFdXecWtsrn6w?sheet=7lcr4x&rangeId=7lcr4x_oDAJVCiejl&rangeVer=1)

[] [销量字段颜色不对](https://sqb.feishu.cn/wiki/OwByw4lHhi2xKskFdXecWtsrn6w?sheet=DWuwYy&rangeId=DWuwYy_MHtf75rQdb&rangeVer=1)

[] [1.销量字段颜色不对 2.掌柜推荐图片有白边](https://sqb.feishu.cn/wiki/OwByw4lHhi2xKskFdXecWtsrn6w?sheet=DWuwYy&rangeId=DWuwYy_s7LHCcrRna&rangeVer=1)

[x] [推荐栏位活动标签过长](https://sqb.feishu.cn/wiki/OwByw4lHhi2xKskFdXecWtsrn6w?sheet=13d5fe&rangeId=13d5fe_rt5mMo2PXV&rangeVer=1)
[] 网格模式的骨架屏问题
[x] [【微信久久折】【鸿蒙】【收钱吧餐饮】【轻餐】【点单页面】通过商品详情页面增加库存商品超过库存限制](https://jira.wosai-inc.com/browse/SMART-22623)
[] 再来一单，扫一扫，购物车关闭问题
[] scrolling 时不渲染数据

## 2025-07-09 隐私协议授权检查功能实现 (参见: doc/privacy-auth-migration-2025-07-09.md)
[x] 创建隐私协议授权检查技术方案文档 doc/privacy-auth-migration-2025-07-09.md
[x] 在submit/index.ts中添加requestPolicyWithBridge等必要导入
[x] 复制requestPolicy方法到submit/index.ts并保留拒绝处理逻辑
[x] 在submit/index.ts第372行附近添加授权检查调用
[x] 实现外卖场景下的授权拒绝处理（阻止下单）
[x] 从home/index.ts中移除2406-2417行的拒绝处理逻辑
## 验证流程
[x] 修复home/index.ts中requestPolicy的存储检查和状态保存逻辑
[x] 修复submit/index.ts中requestPolicy的存储检查，去除storeId维度
[x] 将隐私授权检查逻辑提取到@utils中统一管理
[] 检查TypeScript语法和类型错误
[] 运行ESLint检查代码规范
[] 执行构建测试确保代码可编译
[] 运行单元测试验证功能正确性
[] 测试submit页面授权流程的完整性
[] 验证home页面授权逻辑保持正常
[] 验证授权状态存储和读取功能
[] 验证SLS日志上报的授权检查功能
[] 测试微信和支付宝环境下的兼容性
[] 测试异常情况下的错误处理
[] 测试授权检查对页面性能的影响

## 提交流程
[x] 提交所有代码修改并写commit message
[] 创建 Pull Request 并写明修改说明
[] 更新技术方案文档的实施状态

## 2025-07-15 隐私场景上报功能实现 (参见: doc/privacy-scene-reporting-2025-07-15.md)

### 阶段1：baseBehavior 方法实现 (预计0.5天)
[x] 在 src/behaviors/base.ts 中添加 reportPrivacyScene 方法
[x] 实现 reportPrivacySceneOnce 去重版本方法  
[x] 添加内部辅助方法 _getPrivacyReportedKey
[x] 新增 reportLocationPrivacyScene 专用方法（最低风险方案）
[x] 确保与现有 bridge 访问模式一致

### 阶段2：去重机制实现 (预计0.5天)
[x] 基于 traceId 和场景名创建去重标识
[x] 使用内存 Map 存储已上报场景（会话级去重）
[x] 添加可选的持久化存储去重（跨会话去重c2f193ae7041bded00badc6d9b32477615fd5424）
[x] 实现去重清理机制

### 阶段3：场景集成开发 (预计1-2天)
[x] 扫码场景集成 - src/pages/home/<USER>
[x] 手机号授权场景集成 - src/pages/home/<USER>
[x] 配置化设计实现 - 在 base.ts 中添加 PRIVACY_SCENE_CONFIG 映射
[x] 参数格式调整 - 从 {sceneCode, infoCode} 改为配置key查找
[x] 技术方案文档更新 - 添加配置化方案说明
[x] 新增5个隐私场景配置和实施：
  [x] 相机/相册访问场景 - page-refund-submit-chooseImage
  [x] 地址选择场景 - page-address-chooseLocation  
  [x] 当前位置获取场景 - page-purchased-list-getLocation
  [x] 地图打开场景 - page-home-merchantDetail-openLocation
  [x] 提交页面手机号授权场景 - page-submit-authPhone
[] 新增手机号使用场景上报：
  [x] 提交页面tapAsync钩子中添加手机号使用上报 - page-submit-phone-${serviceTypeName}
  [x] 基于服务类型动态生成配置键支持多种订单类型
  [x] 更新技术方案文档添加tapAsync场景说明

### 阶段4：测试用例开发 (预计1天)
[x] 为 baseBehavior 隐私上报方法编写单元测试
[x] 测试去重机制正确性
[x] 测试不同组件中的调用效果
[x] 测试平台方法不存在时的降级处理
[x] 验证不影响现有业务逻辑
[x] 测试配置化方案的类型检查和错误处理

### 阶段5：代码质量检查 (预计0.5天)
[x] TypeScript 类型检查和语法验证
[x] ESLint 代码规范检查
[x] 构建测试确保代码可编译
[x] 单元测试覆盖率验证

### 阶段6：文档和提交 (预计0.5天)
[x] 更新技术方案文档实施状态
[x] 更新技术方案文档以匹配当前base.ts实际配置格式
[x] 更新 TODO.md 完成状态
[] 创建 Pull Request 并写明修改说明
[] 代码 Review 和合并
