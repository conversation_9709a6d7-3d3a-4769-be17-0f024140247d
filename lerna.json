{"packages": ["dist/*"], "npmClient": "npm", "stream": true, "command": {"bootstrap": {"hoist": true, "noCi": true, "npmClientArgs": ["--no-package-lock"]}, "version": {"conventionalCommits": true, "message": "chore(release): publish %s", "forcePublish": true, "exact": true, "yes": true, "ignoreChanges": ["dist/home/<USER>", "dist/home/<USER>", "dist/home/<USER>"]}, "publish": {"registry": "https://registry-npm.wosai-inc.com/", "allowBranch": ["master", "feature/*", "release/*"], "conventionalCommits": true, "verifyAccess": false, "yes": true, "message": "chore(release): publish %s", "ignoreChanges": ["**/*.md"], "forcePublish": true, "exact": true}}, "ignoreChanges": ["**/*.md", "lerna.json", ".giti<PERSON>re", ".npmrc", "package.json", "dist/**/*", "**/*.test.js", "dist/home/<USER>", "dist/home/<USER>", "dist/home/<USER>"], "version": "1.24.0"}