module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  testEnvironmentOptions: {
    url: 'http://localhost/',
    html: '<!DOCTYPE html><html><head></head><body></body></html>'
  },
  moduleNameMapper: {
    '@services': '<rootDir>/src/lib/services/index.ts',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@wosai/(.*)$': '<rootDir>/node_modules/@wosai/$1',
    '^@mpBridgeExtend$': '<rootDir>/src/mpBridgeExtend',
    '^@utils$': '<rootDir>/src/utils/__mocks__/index.ts',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@BaseComponent$': '<rootDir>/src/BaseComponent',
    '@behaviors': '<rootDir>/src/behaviors',
    '^@wosai/emenu-mini-lodash$': 'lodash',
    '^@wosai/emenu-mini-utils$': '<rootDir>/src/__mocks__/@wosai/emenu-mini-utils.ts',
    '^@wosai/emenu-mini-config$': '<rootDir>/src/__mocks__/@wosai/emenu-mini-config.ts',
    '^@wosai/emenu-mini-emenu$': '<rootDir>/src/__mocks__/@wosai/emenu-mini-config.ts',
    '^@wosai/emenu-mini-services$': '<rootDir>/src/__mocks__/@wosai/emenu-mini-services.ts',
    '^@wosai/emenu-mini-dayjs$': 'dayjs',
    '^@wosai/emenu-mini-md5$': '<rootDir>/src/__mocks__/@wosai/emenu-mini-md5.ts'
  },
  setupFiles: ['<rootDir>/jest.setup.js'],
  transformIgnorePatterns: [
    'node_modules/(?!(@wosai/emenu-mini-utils|@wosai/emenu-mini-lodash|@wosai/emenu-mini-emenuX|@wosai/emenu-mini-emenu|@wosai/emenu-mini-md5)/)'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.test.json',
        isolatedModules: true,
        useESM: true
      }
    ],
    '^.+\\.(js|jsx)$': 'babel-jest'
  },
  testMatch: ['**/__tests__/**/*.[jt]s?(x)', '**/?(*.)+(spec|test).[jt]s?(x)'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  testPathIgnorePatterns: ['/node_modules/'],
  globals: {
    'ts-jest': {
      isolatedModules: true,
      diagnostics: {
        warnOnly: true
      }
    }
  }
}
