{"extends": "./tsconfig.json", "compilerOptions": {"types": ["jest", "node"], "esModuleInterop": true, "isolatedModules": true, "module": "commonjs", "moduleResolution": "node", "allowJs": true, "checkJs": true, "resolveJsonModule": true, "jsx": "react", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@behaviors": ["src/behaviors"], "@behaviors/*": ["src/behaviors/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "src/**/*.spec.ts", "src/**/*.test.ts"]}