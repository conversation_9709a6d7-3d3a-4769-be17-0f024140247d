{
  "compilerOptions": {
    "esModuleInterop": true,
    "outDir": "dist",
    "baseUrl": ".",
    "sourceMap": true,
    "strict": true,
    "importHelpers": true, // 添加这行
    "strictNullChecks": true,
    "noImplicitAny": true,
    "module": "CommonJS",
    "target": "ES5",
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "alwaysStrict": false,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictPropertyInitialization": true,
    "lib": ["ES2020", "dom"],
    "types": ["@types/jest", "node", "miniprogram-api-typings"],
    "paths": {
      "@mpBridgeExtend": ["src/mpBridgeExtend"],
      "@behaviors": ["src/behaviors/index"],
      "@wxs/*": ["src/wxs/*"],
      "@wxs/utils.wxs": ["src/wxs/utils.wxs"],
      "@events": ["src/utils/events/index"],
      "@utils": ["src/utils/index.ts"],
      // "@utils/mixins/aop": ["src/utils/mixins/aop"],
      "@utils/*": ["src/utils/*"],
      "@types/*": ["@types/*"],
      "@BaseComponent": ["src/BaseComponent"],
      "@plugins": ["src/plugins/index"],
      "@sqb/smart-mp-stores": ["src/stores/index"],
      "@wosai/smart-mp-ui/*": ["src/lib/ui/*"],
      "@services": ["src/lib/services/index"],
      "@components_v2/*": ["src/components_v2/*"],
      "@": ["src/"]
    },
    "skipLibCheck": true
  },
  "include": ["src/**/*", "@types/**/*.ts", "tests/**/*", "smart.config.ts"]
}
