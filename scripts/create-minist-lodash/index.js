let path = require('path')

const { Project, SyntaxKind, Node, ImportSpecifier } = require('ts-morph')

const fse = require('fs-extra')

let glob = require('glob')

const resolve = params => path.resolve(__dirname, `../../${params}`)

const { exec } = require('child_process')

let config = {
  parser: {
    include: [
      new RegExp(resolve('src')),
      new RegExp(resolve('node_modules/@wosai/smart-mp')),
      new RegExp(resolve('node_modules/@wosai/emenu-mini'))
    ],
    exclude: [new RegExp(resolve('node_modules/@wosai/smart-mp-lodash'))],

    lodashPackageName: '@wosai/emenu-mini-lodash'
  },

  create: {
    localLodashEntry: '/Users/<USER>/Desktop/work/smart-mp-lodash/lodash.custom.js',
    localLodashOutPut: '/Users/<USER>/Desktop/work/smart-mp-lodash/lodash.custom.min.js',
    manualAddLists: [],
    filterLists: ['chain']
  }
}

function isConditionFile(file, config) {
  if (!config) return false
  const { include = [], exclude = [] } = config
  const isInclude = include.some(reg => reg.test(file))
  const isExclude = exclude.some(reg => reg.test(file))

  if (!include.length && !exclude.length) {
    return true
  } else if (!include.length) {
    return isExclude
  } else if (!exclude.length) {
    return isInclude
  } else {
    return isInclude && !isExclude
  }
}

async function run() {
  let pattern = resolve('**/*.{t,j}s')

  let files = glob.sync(pattern).filter(file => {
    return isConditionFile(file, config.parser)
  })

  let context = {
    name_sets: new Set()
  }
  parserFiles(files, context)

  createMinistLodash(context)
}

function createMinistLodash(context) {
  let lists = Array.from(context.name_sets)

  lists = lists.concat(config.create.manualAddLists)
  let functionNames = lists.filter(name => {
    return !config.create.filterLists.includes(name)
  })

  let contents = ''

  functionNames.forEach(name => {
    contents += `
              import { default as ${name}} from "./${name}.js";
                export { ${name}};
          `
  })

  contents += `
    const defaultLodash = {
      ${functionNames.join(',\n')}
    };
    export default defaultLodash
  `

  fse.writeFileSync(config.create.localLodashEntry, contents)

  const command = `rollup ${config.create.localLodashEntry} --file ${config.create.localLodashOutPut} --format esm --plugin terser`

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`执行命令失败: ${error}`)
      return
    }
    if (stderr) {
      console.error(`标准错误: ${stderr}`)
      return
    }
  })
}

function parserFiles(files, context) {
  files.forEach(file => {
    parseFile(file, context)
  })
}

function parseFile(file, context) {
  const project = new Project()

  const sourceFile = project.addSourceFileAtPath(file)

  let lodashImportDeclarations = sourceFile
    .getDescendantsOfKind(SyntaxKind.ImportDeclaration)
    .filter(declaration => {
      const moduleSpecifier = declaration.getModuleSpecifier()
      return (
        moduleSpecifier.getKind() === SyntaxKind.StringLiteral &&
        moduleSpecifier.getLiteralText() === config.parser.lodashPackageName
      )
    })

  if (!lodashImportDeclarations.length) {
    return
  }

  lodashImportDeclarations.forEach(lodashImportDeclaration => {
    let importDeclarations = lodashImportDeclaration.getNamedImports()
    importDeclarations.forEach(importer => {
      const name = importer.getName()
      context.name_sets.add(name)
    })

    let defaulterImportName
    let defaultImport = lodashImportDeclaration.getDefaultImport()

    if (!defaultImport) return

    defaulterImportName = defaultImport.getText()

    sourceFile
      .getDescendantsOfKind(SyntaxKind.PropertyAccessExpression)
      .forEach(propertyAccessExpression => {
        let parent = propertyAccessExpression.getParent()

        if (
          Node.isPropertyAccessExpression(parent) &&
          parent.getExpression() !== propertyAccessExpression
        )
          return

        let expression = propertyAccessExpression.getExpression()

        let name = propertyAccessExpression.getName()

        if (
          expression.getKind() === SyntaxKind.Identifier &&
          expression.getText() === defaulterImportName
        ) {
          context.name_sets.add(name)
        }
      })
  })
}

run()
