const { execSync, spawn, exec } = require('child_process')
const fs = require('fs')
const path = require('path')
const { startOra } = require('./utils')
const chalk = require('chalk')

const spinner = startOra('初始化项目@wosai/smart-mp-biz...').start()
spinner.prefixText = chalk.yellow('[smart]')

const submodules = [
  // {
  //   name: 'smart-mp-ui',
  //   path: path.resolve(__dirname, '../src/lib/smart-mp-ui'), // 替换为子模块的路径
  //   url: '*********************:MK/miniprogram/smart-mp-ui.git'
  // },
  {
    name: 'smart-mp-dev',
    path: path.resolve(__dirname, '../smart-mp-dev'), // 替换为子模块的路径
    url: '*********************:MK/miniprogram/smart-mp-dev.git'
  },
  {
    name: 'smart-mp-dev-alipay',
    path: path.resolve(__dirname, '../smart-mp-dev-alipay'), // 替换为子模块的路径
    url: '*********************:MK/miniprogram/smart-mp-dev.git',
    branch: 'feature/dev-alipay'
  }
]

submodules.forEach(submodule => {
  const submodulePath = path.resolve(__dirname, submodule.path)
  if (!fs.existsSync(submodulePath)) {
    spinner.text = `正在克隆子模块${submodule.name}...`
    execSync(
      `git clone ${submodule.branch ? `-b ${submodule.branch}` : ''} ${submodule.url} ${
        submodule.path
      }`,
      { stdio: 'inherit' }
    )
  } else {
    spinner.text = `子模块${submodule.name}已存在于${submodule.path}...`
  }
})

// git
spinner.text = '正在初始化和更新子模块...'
execSync('git submodule update --init --recursive', { stdio: 'inherit' })

spinner.succeed('初始化项目成功！')
