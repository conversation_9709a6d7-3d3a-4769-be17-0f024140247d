const fs = require('fs')
const path = require('path')
const request = require('request')

const env = process.env.NODE_ENV || 'dev'

const lernaJSON = fs.readFileSync(path.resolve('./lerna.json'), 'utf8')

try {
  const {
    COMMIT_MESSAGE,
    CI_COMMIT_AUTHOR,
    CI_COMMIT_TITLE,
    CI_COMMIT_MESSAGE,
    CI_COMMIT_DESCRIPTION,
    CI_JOB_URL,
    CI_MERGE_REQUEST_DIFF_ID,
    CI_COMMIT_TAG,
    CI_COMMIT_BRANCH
  } = process.env
  let version
  if (env === 'dev') {
    ;({ version } = JSON.parse(lernaJSON))
  }

  const url = 'https://open.feishu.cn/open-apis/bot/v2/hook/9e723bd2-6c3c-4cbd-af5d-1118b47d62a8'
  const content = `🎉 smart-mp-biz 发布成功

📦 版本信息
**版本号：** ${CI_COMMIT_TAG || version}
**分支：** ${CI_COMMIT_BRANCH}
**提交者：** ${CI_COMMIT_AUTHOR || '未知'}

📝 提交信息
${CI_COMMIT_MESSAGE || COMMIT_MESSAGE}
${CI_COMMIT_DESCRIPTION ? `📋 详细描述 ${CI_COMMIT_DESCRIPTION}` : ''}
🔍 NPM 包链接
⚠️ 请检查以下 NPM 包是否成功发布：

- [📦 smart-mp-biz-home](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-biz-home) ✅ 点单首页及轻餐结算
- [📦 smart-mp-biz-order](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-biz-order) ✅ 围餐、订单详情及列表等
- [📦 smart-mp-biz-center](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-biz-center) ✅ 平台中心[我的]
- [📦 smart-mp-biz-love](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-biz-love) ✅ 爱心餐 
- [📦 smart-mp-biz-store](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-biz-store) ✅ 店铺详情页

${CI_JOB_URL ? `🔗 [查看构建详情](${CI_JOB_URL})` : ''}`

  let jsonData = {
    msg_type: 'interactive',
    card: {
      config: {
        wide_screen_mode: true
      },
      elements: [
        {
          tag: 'markdown',
          content
        }
      ],
      header: {
        template: CI_COMMIT_TAG ? 'green' : 'blue',
        title: {
          content: `📦 NPM 包${CI_COMMIT_TAG ? '正式' : '测试'}版本发布通知`,
          tag: 'plain_text'
        }
      }
    }
  }

  const options = {
    url,
    headers: {
      'Content-Type': 'application/json'
    },
    json: jsonData
  }

  request.post(options, (error, response, body) => {
    console.log('飞书通知:', content)
  })
} catch (e) {
  console.error(e)
}
