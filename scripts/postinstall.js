const { execSync, spawn, exec } = require('child_process')
const fs = require('fs')
const path = require('path')
const { startOra } = require('./utils')
const chalk = require('chalk')

const spinner = startOra('初始化项目@wosai/smart-mp-biz...').start()
spinner.prefixText = chalk.yellow('[smart]')

require('./prepare')

const handleExec = (command, options = {}) => {
  try {
    execSync(command, { stdio: 'inherit', ...options })
    return true
  } catch (error) {
    if (command.includes('unlink')) return false

    spinner.warn(`执行命令失败: ${command}`)
    spinner.warn(`错误信息: ${error.message}`)
    return false
  }
}

// npm install
spinner.text = '正在安装依赖...'
console.log(`正在安装依赖`)
const smartMpDevPath = path.resolve(__dirname, '../smart-mp-dev')
execSync('npm install', { cwd: smartMpDevPath, stdio: 'inherit' })
spinner.succeed('smart-mp-dev npm install 安装完成！')

const smartMpDevAlipayPath = path.resolve(__dirname, '../smart-mp-dev-alipay')
execSync('npm install', { cwd: smartMpDevAlipayPath, stdio: 'inherit' })
spinner.succeed('smart-mp-dev-alipay npm install 安装完成！')

// // link
// spinner.text = `链接@wosai/smart-mp-biz 到 smart-mp-dev/src/E1QDEFPQUJWB...`
// //smart-mp-dev/src/E1QDEFPQUJWB
// //smart-mp-dev/src/JDLTQXMWBQMW
// //smart-mp-dev/src/KAWNGVMGQZBZ
// const e1QDEFPQUJWBPath = path.resolve(smartMpDevPath, 'src/E1QDEFPQUJWB')
// const JDLTQXMWBQMW = path.resolve(smartMpDevPath, 'src/JDLTQXMWBQMW')
// const GDLPKTFLUN7C = path.resolve(smartMpDevPath, 'src/GDLPKTFLUN7C')
// const KAWNGVMGQZBZ = path.resolve(smartMpDevPath, 'src/KAWNGVMGQZBZ')
//
// // 先解除所有 link
// const unLinkCommands = [
//   { cwd: e1QDEFPQUJWBPath, cmd: 'yarn unlink "@wosai/smart-mp-biz"' },
//   { cwd: JDLTQXMWBQMW, cmd: 'yarn unlink "@wosai/smart-mp-biz"' },
//   { cwd: GDLPKTFLUN7C, cmd: 'yarn unlink "@wosai/smart-mp-biz"' },
//   { cwd: KAWNGVMGQZBZ, cmd: 'yarn unlink "@wosai/smart-mp-biz"' }
// ]
//
// // 解除全局 link
// // handleExec('yarn unlink')
//
// // 解除各目录的 link
// unLinkCommands.forEach(({ cwd, cmd }) => {
//   handleExec(cmd, { cwd })
// })
//
// // 重新建立 link
// handleExec('yarn link')
//
// // 重新链接各目录
// const linkCommands = [
//   { cwd: e1QDEFPQUJWBPath, cmd: 'yarn link "@wosai/smart-mp-biz"' },
//   { cwd: JDLTQXMWBQMW, cmd: 'yarn link "@wosai/smart-mp-biz"' },
//   { cwd: GDLPKTFLUN7C, cmd: 'yarn link "@wosai/smart-mp-biz"' },
//   { cwd: KAWNGVMGQZBZ, cmd: 'yarn link "@wosai/smart-mp-biz"' }
// ]
//
// linkCommands.forEach(({ cwd, cmd }) => {
//   handleExec(cmd, { cwd })
//   spinner.succeed(`重新链接成功: ${cwd}`)
// })
//
// spinner.succeed('安装完成')
