import { defineConfig } from '@morjs/cli'
import { createBaseConfig, createTargetConfig } from './build/utils/config'
import path from 'path'

const packageName = process.env.package_name

/**
 * 根据 package_name 生成不同的配置
 * 满足 home、order、center、love 等不同业务
 * 如果需要新增业务，只需要新增component.[package-name].json 中新增页面
 */
const baseConfig = createBaseConfig(
  {
    'component.json': packageName
      ? path.resolve(__dirname, `./src/component.${packageName}.json`)
      : path.resolve(__dirname, './src/component.json')
  },
  packageName
)

const wechatConfig = {
  ...baseConfig,
  define: {
    'process.env.PLATFORM': 'wechat'
  },
  conditionalCompile: {
    context: {},
    fileExt: '.wx'
  }
}

export default defineConfig([
  createTargetConfig(
    wechatConfig,
    'wechat',
    packageName ? `./dist/${packageName}/wechat` : './dist/biz/wechat',
    packageName || ''
  ),
  createTargetConfig(
    {
      ...baseConfig,
      autoInjectRuntime: {
        api: 'enhanced'
      },
      externals: [],
      define: {
        'process.env.PLATFORM': 'alipay'
      },
      conditionalCompile: {
        fileExt: '.my'
      },
      processNodeModules: {
        include: [/^@morjs\//]
      }
    },
    'alipay',
    packageName ? `./dist/${packageName}/alipay` : './dist/biz/alipay',
    packageName || ''
  ),
  createTargetConfig(
    baseConfig,
    'web',
    packageName ? `./dist/${packageName}/web` : './dist/biz/web',
    packageName || ''
  )
])
