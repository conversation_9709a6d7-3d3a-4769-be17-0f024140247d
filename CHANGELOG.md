# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.1.3-alpha.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.8.0...v1.1.3-alpha.0) (2024-11-16)


### Features

* add lerna ([39ef8f5](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/39ef8f53c7cc7475bcbb4a6fae27915c87c06543))
* add lerna ([a03e78b](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/a03e78b486bae012e38e88ad57dba32d9f605b84))
* add lerna ([19c7e16](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/19c7e162c10ff6e5e6af8d9baa8ff72a51ff8bb9))
* add lerna ([12f14e2](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/12f14e276022d09845d2ba12a1d0ed7f64db4755))
* add lerna ([0d07ad5](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/0d07ad58f401c5ba48239795f046d1d3311f8c5c))
* add lerna ([2900207](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/290020741781ecafbf8d431d406030771a76c88f))





## [1.1.2-alpha.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.8.0...v1.1.2-alpha.0) (2024-11-16)


### Features

* add lerna ([a03e78b](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/a03e78b486bae012e38e88ad57dba32d9f605b84))
* add lerna ([19c7e16](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/19c7e162c10ff6e5e6af8d9baa8ff72a51ff8bb9))
* add lerna ([12f14e2](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/12f14e276022d09845d2ba12a1d0ed7f64db4755))
* add lerna ([0d07ad5](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/0d07ad58f401c5ba48239795f046d1d3311f8c5c))
* add lerna ([2900207](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/290020741781ecafbf8d431d406030771a76c88f))





## [1.1.1-alpha.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.8.0...v1.1.1-alpha.0) (2024-11-16)


### Features

* add lerna ([19c7e16](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/19c7e162c10ff6e5e6af8d9baa8ff72a51ff8bb9))
* add lerna ([12f14e2](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/12f14e276022d09845d2ba12a1d0ed7f64db4755))
* add lerna ([0d07ad5](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/0d07ad58f401c5ba48239795f046d1d3311f8c5c))
* add lerna ([2900207](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/290020741781ecafbf8d431d406030771a76c88f))





# [1.2.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.8.0...v1.2.0) (2024-11-16)


### Features

* add lerna ([12f14e2](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/12f14e276022d09845d2ba12a1d0ed7f64db4755))
* add lerna ([0d07ad5](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/0d07ad58f401c5ba48239795f046d1d3311f8c5c))
* add lerna ([2900207](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/290020741781ecafbf8d431d406030771a76c88f))





# [1.1.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.8.0...v1.1.0) (2024-11-16)


### Features

* add lerna ([0d07ad5](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/0d07ad58f401c5ba48239795f046d1d3311f8c5c))
* add lerna ([2900207](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/290020741781ecafbf8d431d406030771a76c88f))





# [1.9.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.8.0...v1.9.0) (2024-11-16)


### Features

* add lerna ([2900207](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/290020741781ecafbf8d431d406030771a76c88f))





# [1.8.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.7.0...v1.8.0) (2024-11-16)

### Features

- add lerna ([d99ec17](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/d99ec1795e25e6bdcddae739da7455febb86386e))

# [1.8.0](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/compare/v1.7.0...v1.8.0) (2024-11-16)

### Features

- add lerna ([d99ec17](https://git.wosai-inc.com/MK/miniprogram/smart-mp-biz/commits/d99ec1795e25e6bdcddae739da7455febb86386e))
