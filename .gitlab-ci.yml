image: jfrog.wosai-inc.com/docker-virtual-prod/middleware/node-builder:20

stages:
  - deploy

stages:
  - deploy

cache:
  paths:
    - node_modules
  # 添加缓存策略
  key: ${CI_COMMIT_REF_SLUG}
  policy: pull-push
  # 排除补丁相关的文件和目录
  untracked: false
  when: 'always'

variables:
  NODE_OPTIONS: --max-old-space-size=32768
  NPM_CONFIG_REGISTRY: https://registry-npm.wosai-inc.com

before_script:
  - node --version
  # 设置 npm 配置
  - npm config set registry https://registry-npm.wosai-inc.com
  - npm config set strict-ssl false
  - echo "Node.js 详细信息："
  - node -p "process.versions"
  - echo "当前环境变量："
  - env | grep NODE
  - npm config set strict-ssl false
  # 安装依赖
  - rm -rf node_modules
  - npm install --ignore-scripts
  # 检查初始文件状态
  - echo "Initial files:"
  - ls -la node_modules/@morjs/runtime-mini/lib/alipay || true
  # 执行第一次构建，生成补丁文件
  - echo "Checking patch file:"
  - ls -la build/plugins/patch
  # 应用补丁
  - echo "Applying patch..."
  - mkdir -p node_modules/@morjs/runtime-mini/lib/alipay
  - cp build/plugins/patch/componentToAlipay.js.patch node_modules/@morjs/runtime-mini/lib/alipay/componentToAlipay.js
  # 检查补丁是否应用成功
  - echo "After patch files:"
  - ls -la node_modules/@morjs/runtime-mini/lib/alipay
  # 再次执行构建
  - npm run build:all
  # Git 配置
  - |
    git config --global user.name "${GITLAB_USER_NAME}"
    git config --global user.email "${GITLAB_USER_EMAIL}"
    git config --global core.warnambiguousrefs false
    git remote set-url origin "https://gitlab-ci-token:$GIT_ACCESS_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git"

deploy-feature:
  stage: deploy
  tags:
    - jfrog-dev
  script:
    - |
      COMMIT_MESSAGE=$(git log -1 --pretty=%B)
      echo $CI_COMMIT_AUTHOR
      echo $CI_COMMIT_TITLE
      echo $CI_COMMIT_MESSAGE
      echo $CI_COMMIT_DESCRIPTION
      echo $CI_JOB_URL
      echo $CI_MERGE_REQUEST_DIFF_ID
      echo $CI_COMMIT_BRANCH
    
    - |
      git checkout -B "$CI_COMMIT_REF_NAME" "$CI_COMMIT_SHA"
      
      # 1. 更新版本
      npx lerna version prepatch \
        --force-publish "*" \
        --yes \
        --no-git-tag-version \
        --conventional-commits \
        --ignore-changes "**/*" \
        --loglevel verbose
      
      # 2. 提交更改
      git add .
      git commit -m "bump new beta version"
      
      # 3. 发布包
      npx lerna publish from-package \
        --force-publish \
        --no-git-reset \
        --yes \
        --loglevel verbose \
        --registry https://registry-npm.wosai-inc.com/
    
    - NODE_ENV=dev node ./scripts/notify.js
  only:
    - /^(feature|release).*$/

deploy-release:
  stage: deploy
  tags:
    - jfrog-dev
  script:
    - |
      COMMIT_MESSAGE=$(git log -1 --pretty=%B)
      echo $CI_COMMIT_AUTHOR
      echo $CI_COMMIT_TITLE
      echo $CI_COMMIT_MESSAGE
      echo $CI_COMMIT_DESCRIPTION
      echo $CI_JOB_URL
      echo $CI_MERGE_REQUEST_DIFF_ID
      echo $CI_COMMIT_BRANCH
    
    - |
      git checkout -B "master" "$CI_COMMIT_SHA"
      npx lerna version "$CI_COMMIT_TAG" --force-publish -y --no-git-tag-version --conventional-commits
      git add .
      git commit -m "bump new release version"
      npx lerna publish from-package \
        --force-publish \
        --no-git-reset \
        --yes \
        --loglevel verbose \
        --registry https://registry-npm.wosai-inc.com/
    
    - node ./scripts/notify.js
  only:
    - tags

deploy-pipeline:
  stage: deploy
  tags:
    - jfrog-dev
  script:
    - |
      COMMIT_MESSAGE=$(git log -1 --pretty=%B)
      echo $CI_COMMIT_AUTHOR
      echo $CI_COMMIT_TITLE
      echo $CI_COMMIT_MESSAGE
      echo $CI_COMMIT_DESCRIPTION
      echo $CI_JOB_URL
      echo $CI_MERGE_REQUEST_DIFF_ID
      echo $CI_COMMIT_BRANCH
    
    - |
      git checkout -B "master" "$CI_COMMIT_SHA"
      git add .
      git commit -m "bump new beta version by trigger"
    
    - node ./scripts/notify.js
  only:
    - triggers
